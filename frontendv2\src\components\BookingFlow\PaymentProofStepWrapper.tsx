import { useNavigate, useSearchParams } from 'react-router-dom';
import PaymentProofStep from './PaymentProofStep';

export default function PaymentProofStepWrapper() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const handleBack = () => {
    const params = new URLSearchParams();
    const serviceParam = searchParams.get('service');
    const addonsParam = searchParams.get('addons');
    const date = searchParams.get('date');
    const time = searchParams.get('time');
    
    if (serviceParam) params.set('service', serviceParam);
    if (addonsParam) params.set('addons', addonsParam);
    if (date) params.set('date', date);
    if (time) params.set('time', time);
    
    navigate(`/booking/details?${params.toString()}`);
  };



  return (
    <PaymentProofStep
      onBack={handleBack}
    />
  );
}
