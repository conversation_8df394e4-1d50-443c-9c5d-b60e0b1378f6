import { useState } from 'react';
import { authAPI, handleApiError } from '../../utils/api';
import PasswordInput from '../PasswordInput';

interface LoginProps {
  onLogin: (user: any) => void;
  onSwitchToSignup?: () => void;
}

export default function Login({ onLogin }: LoginProps) {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [checkingUser, setCheckingUser] = useState(false);
  const [userInfo, setUserInfo] = useState<{
    exists: boolean;
    isAdmin: boolean;
    requiresPassword: boolean;
    loginMethod: string;
    message: string;
    user?: any;
  } | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    setError(''); // Clear error when user types

    // Check if user is admin when email changes
    if (e.target.name === 'email') {
      checkIfUserIsAdmin(e.target.value);
    }
  };

  const checkIfUserIsAdmin = async (emailOrPhone: string) => {
    if (!emailOrPhone) {
      setUserInfo(null);
      return;
    }

    setCheckingUser(true);
    try {
      // Check if input is email or phone
      const isEmail = emailOrPhone.includes('@');
      let response;

      if (isEmail) {
        response = await authAPI.checkEmail(emailOrPhone);
      } else {
        // For phone numbers, we'll use a different endpoint or modify the existing one
        response = await authAPI.checkEmailOrPhone(emailOrPhone);
      }

      if (response.success && response.data) {
        setUserInfo({
          exists: response.data.exists,
          isAdmin: response.data.isAdmin,
          requiresPassword: response.data.requiresPassword,
          loginMethod: response.data.loginMethod,
          message: response.data.message,
          user: response.data.user
        });
      } else {
        setUserInfo(null);
      }
    } catch (error) {
      console.error('Error checking user:', error);
      setUserInfo(null);
    } finally {
      setCheckingUser(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      let response;

      if (userInfo?.isAdmin && userInfo?.requiresPassword) {
        // Admin login with password
        if (!formData.password) {
          setError('Password is required for admin login');
          setIsLoading(false);
          return;
        }
        response = await authAPI.login(formData.email, formData.password);

        // For admin login, the user data is in response.data.user
        if (response.success && response.data?.user) {
          onLogin(response.data.user);
        } else {
          setError(response.message || 'Login failed');
        }
      } else if (userInfo?.exists && userInfo?.loginMethod === 'email-only') {
        // Regular user login with email only
        const isEmail = formData.email.includes('@');
        if (isEmail) {
          response = await authAPI.loginEmailOnly(formData.email);
        } else {
          response = await authAPI.loginWithEmailOrPhone(formData.email);
        }

        // For regular user login, the response contains the user data
        if (response.success) {
          onLogin(response.data?.user || {});
        } else {
          setError(response.message || 'Login failed');
        }
      } else {
        // User doesn't exist or needs registration
        setError('Please check your email/phone or create a new account');
        setIsLoading(false);
        return;
      }
    } catch (err) {
      setError(handleApiError(err));
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <h2>Welcome</h2>
          <p>Sign in to access your account</p>
        </div>

        <form onSubmit={handleSubmit} className="auth-form">
          {error && <div className="error-message">{error}</div>}

          <div className="form-group">
            <label htmlFor="email">Email Address or Phone Number</label>
            <input
              type="text"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              required
              placeholder="Enter your email or phone number"
            />
            {checkingUser && (
              <small style={{ color: '#666', fontSize: '0.8rem' }}>
                Checking user type...
              </small>
            )}
            {formData.email && !checkingUser && userInfo && (
              <small style={{ color: '#666', fontSize: '0.8rem' }}>
                {userInfo.isAdmin ? 'Admin - password required' : 'Regular user'}
              </small>
            )}
          </div>

          {userInfo?.isAdmin && userInfo?.requiresPassword && (
            <div className="form-group">
              <label htmlFor="password">Password</label>
              <PasswordInput
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                required
                placeholder="Enter your password"
              />
            </div>
          )}

          <button
            type="submit"
            className="auth-submit-button"
            disabled={isLoading}
          >
            {isLoading ? (
              <span className="loading-spinner">
                <span className="spinner"></span>
                Signing In...
              </span>
            ) : (
              'Sign In'
            )}
          </button>
        </form>

        <div className="auth-footer">
          <p style={{ color: '#666', fontSize: '0.9rem', textAlign: 'center' }}>
            New users are automatically created when booking appointments
          </p>
        </div>
      </div>
    </div>
  );
}
