import { useState, useEffect, useCallback } from 'react';
import { API_CONFIG } from '../utils/config';
import './BookingDetails.css';
import microlocImage from '../assets/microloc.jpg';
import { CLOUDINARY_IMAGES } from '../utils/imageConstants';
import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";

interface AddOnService {
  id: string;
  name: string;
  duration: number;
  price: number;
}

interface BookingState {
  selectedService: any;
  selectedAddOns: AddOnService[];
  selectedDate: string;
  selectedTime: string;
  step: 'services' | 'datetime' | 'details' | 'checkout';
}

interface BookingDetailsProps {
  booking: BookingState;
  onBack: () => void;
  onContinue: (customerInfo: any) => void;
}

function BookingDetails({ booking, onBack, onContinue }: BookingDetailsProps) {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    userId: ''
  });

  const [emailExists, setEmailExists] = useState<boolean | null>(null);
  const [isCheckingEmail, setIsCheckingEmail] = useState(false);

  // Debounced email checking
  const checkEmailExists = useCallback(async (email: string) => {
    if (!email || !email.includes('@')) return;

    setIsCheckingEmail(true);
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}/auth/check-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();
      setEmailExists(data.exists);

      if (data.exists && data.user) {
        // Pre-fill form with existing user data
        setFormData(prev => ({
          ...prev,
          firstName: data.user.firstName || prev.firstName,
          lastName: data.user.lastName || prev.lastName,
          phone: data.user.phone || prev.phone,
          userId: data.user.id // Store the user ID for later use
        }));
      }
    } catch (error) {
      console.error('Error checking email:', error);
      setEmailExists(null);
    } finally {
      setIsCheckingEmail(false);
    }
  }, []);

  // Debounce email checking
  useEffect(() => {
    const timer = setTimeout(() => {
      if (formData.email) {
        checkEmailExists(formData.email);
      } else {
        setEmailExists(null);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [formData.email, checkEmailExists]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;

    // Auto-add + prefix for phone numbers
    if (e.target.name === 'phone') {
      // Remove any non-digit characters except +
      value = value.replace(/[^\d+]/g, '');

      // Ensure it starts with + if user enters digits
      if (value.length > 0 && !value.startsWith('+')) {
        value = '+' + value;
      }
    }

    setFormData({
      ...formData,
      [e.target.name]: value
    });
  };

  const handleSubmit = () => {
    // Check for missing required fields
    const missingFields = [];
    if (!formData.firstName) missingFields.push('First Name');
    if (!formData.lastName) missingFields.push('Last Name');
    if (!formData.phone) missingFields.push('Phone Number');
    if (!formData.email) missingFields.push('Email Address');

    if (missingFields.length > 0) {
      alert(`Please fill in the following required fields: ${missingFields.join(', ')}`);
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      alert('Please enter a valid email address.');
      return;
    }

    // Validate phone number starts with +
    if (formData.phone && !formData.phone.startsWith('+')) {
      alert('Phone number must start with + followed by country code (e.g., +1234567890)');
      return;
    }

    onContinue(formData);
  };

  return (
    <div className="app">
      <header className="header">
        <div className="header-content">
          <div className="auth-links">
            <a href="#" className="auth-link">SIGN UP</a>
            <a href="#" className="auth-link">LOG IN</a>
          </div>
        </div>
      </header>

      <main className="main-content">
        <div className="booking-details">
          <div className="booking-header">
            <button className="back-button" onClick={onBack}>
              ← Back to Date & Time
            </button>
            <h1>Your Information</h1>
          </div>



          {/* Microloc Information Section */}
          <div className="microloc-info-section">
            <div className="microloc-image-container">
              <img
                src={CLOUDINARY_IMAGES.MICROLOC}
                alt="Microlocs hairstyle example"
                className="microloc-image"
                onError={(e) => {
                  console.warn('Cloudinary microloc image failed, trying fallbacks');
                  const img = e.currentTarget as HTMLImageElement;
                  if (img.src.includes('cloudinary.com')) {
                    img.src = microlocImage; // Fallback to imported asset
                  } else if (img.src === microlocImage) {
                    img.src = '/microloc.jpg'; // Fallback to public folder
                  } else {
                    img.style.display = 'none';
                  }
                }}
              />
            </div>
            <div className="microloc-notice">
              <h4>📖 Please Read Before Proceeding</h4>
              <p>
                Please review the microloc image above and ensure you understand the service details.
                This will help you make an informed decision about your appointment.
              </p>
            </div>
          </div>

          {/* Appointment Summary */}
          <div className="appointment-summary">
            <div className="appointment-card">
              <div className="appointment-details">
                <h4>{booking.selectedService?.name}</h4>
                <p className="appointment-price">${booking.selectedService?.price}</p>
                <p className="appointment-duration">Duration: {booking.selectedService?.duration} hours</p>
                <p className="appointment-datetime">
                  {booking.selectedDate ? new Date(booking.selectedDate).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  }) : 'Date not selected'} at {booking.selectedTime} CDT
                </p>



                {/* Full Service Description */}
                {booking.selectedService?.description && (
                  <div className="service-description">
                    <h5>Service Details:</h5>
                    <div className="description-content">
                      {booking.selectedService.description.split('\n').map((line: string, index: number) => (
                        <p key={index}>{line}</p>
                      ))}
                    </div>
                  </div>
                )}

                {/* Service Images Gallery - Dynamic Gallery */}
                <ServiceImageGallery service={booking.selectedService} />

                {booking.selectedAddOns.map(addOn => (
                  <div key={addOn.id} className="addon-line">
                    + {addOn.name}, {addOn.duration} minutes @ ${addOn.price}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Customer Information Form */}
          <div className="booking-info">
            <div className="info-header">
              <h3>Your Information</h3>
            </div>
            
            <div className="info-content">
              <div className="info-field">
                <label htmlFor="firstName" className="field-label">
                  First Name<span className="required-mark">*</span>
                </label>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  required
                  className="text-input"
                  placeholder="Enter your first name"
                  autoComplete="given-name"
                />
              </div>

              <div className="info-field">
                <label htmlFor="lastName" className="field-label">
                  Last Name<span className="required-mark">*</span>
                </label>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  required
                  className="text-input"
                  placeholder="Enter your last name"
                  autoComplete="family-name"
                />
              </div>

              <div className="info-field">
                <label htmlFor="phone" className="field-label">
                  Phone Number<span className="required-mark">*</span>
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  placeholder="+1234567890"
                  required
                  className="text-input"
                  autoComplete="tel"
                />
                <div className="input-hint">
                  Include country code (e.g., +1 for US, +44 for UK)
                </div>
              </div>

              <div className="info-field">
                <label htmlFor="email" className="field-label">
                  Email Address<span className="required-mark">*</span>
                </label>
                <div className="email-wrapper">
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Enter your email address"
                    className="text-input"
                    autoComplete="email"
                    required
                  />
                  <div className="email-status">
                    {isCheckingEmail && (
                      <span className="status-checking">Checking...</span>
                    )}
                    {emailExists === true && (
                      <span className="status-found">✓ Account found</span>
                    )}
                    {emailExists === false && (
                      <span className="status-new">New account</span>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="info-footer">
              <button
                className="submit-button"
                onClick={handleSubmit}
                disabled={!formData.firstName || !formData.lastName || !formData.phone || !formData.email}
              >
                Continue to Payment
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

// Service Image Gallery Component
interface ServiceImageGalleryProps {
  service: any;
}

const ServiceImageGallery: React.FC<ServiceImageGalleryProps> = ({ service }) => {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Create comprehensive list of all available images
  const allImages = [];

  // Add main image if it exists
  if (service?.image) {
    allImages.push({
      src: service.image,
      alt: `${service.name} - Main Style`,
      isMain: true
    });
  }

  // Add images from images array
  if (service?.images && Array.isArray(service.images)) {
    service.images.forEach((imageUrl: string, index: number) => {
      // Don't duplicate the main image
      if (imageUrl !== service?.image) {
        allImages.push({
          src: imageUrl,
          alt: `${service.name} - Style Option ${index + 1}`,
          isMain: false
        });
      }
    });
  }

  console.log('🔍 Gallery Images:', allImages);

  if (allImages.length === 0) {
    return null;
  }

  const openLightbox = (index: number) => {
    setCurrentImageIndex(index);
    setLightboxOpen(true);
  };

  return (
    <div className="service-gallery-container">
      <h5 className="gallery-title">
        🎨 Available Styles ({allImages.length})
      </h5>

      <div className="gallery-grid">
        {allImages.map((image, index) => (
          <div
            key={index}
            className={`gallery-thumbnail ${image.isMain ? 'main-image' : ''}`}
            onClick={() => openLightbox(index)}
          >
            <img
              src={image.src}
              alt={image.alt}
              className="thumbnail-image"
              onError={(e) => {
                console.error('❌ Failed to load image:', image.src);
                e.currentTarget.style.display = 'none';
              }}
              onLoad={() => {
                console.log('✅ Successfully loaded image:', image.src);
              }}
            />
            {image.isMain && (
              <div className="main-badge">Main</div>
            )}
            <div className="image-overlay">
              <span className="view-text">👁️ View</span>
            </div>
          </div>
        ))}
      </div>

      {lightboxOpen && (
        <Lightbox
          open={lightboxOpen}
          close={() => setLightboxOpen(false)}
          slides={allImages}
          index={currentImageIndex}
        />
      )}
    </div>
  );
};

export default BookingDetails;
