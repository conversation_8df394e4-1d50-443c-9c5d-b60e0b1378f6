import { useNavigate, useSearchParams } from 'react-router-dom'
import BookingDetails from '../BookingDetails'

interface AddOnService {
  id: string;
  name: string;
  duration: number;
  price: number;
}

export default function DetailsStep() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  // Get booking data from URL params
  const serviceParam = searchParams.get('service');
  const addonsParam = searchParams.get('addons');
  const date = searchParams.get('date');
  const time = searchParams.get('time');
  
  let selectedService = null;
  let selectedAddOns: AddOnService[] = [];
  
  if (serviceParam) {
    try {
      selectedService = JSON.parse(serviceParam);
    } catch (error) {
      console.error('Error parsing service:', error);
    }
  }
  
  if (addonsParam) {
    try {
      selectedAddOns = JSON.parse(addonsParam);
    } catch (error) {
      console.error('Error parsing addons:', error);
    }
  }

  const booking = {
    selectedService,
    selectedAddOns,
    selectedDate: date || '',
    selectedTime: time ? decodeURIComponent(time) : '',
    step: 'details' as const
  };

  const handleBack = () => {
    const params = new URLSearchParams();
    params.set('service', JSON.stringify(selectedService));
    if (selectedAddOns.length > 0) {
      params.set('addons', JSON.stringify(selectedAddOns));
    }
    
    navigate(`/booking/datetime?${params.toString()}`);
  };

  const handleContinue = (customerInfo: any) => {
    const params = new URLSearchParams();
    params.set('service', JSON.stringify(selectedService));
    if (selectedAddOns.length > 0) {
      params.set('addons', JSON.stringify(selectedAddOns));
    }
    params.set('date', date || '');
    params.set('time', encodeURIComponent(time || ''));
    params.set('customerInfo', JSON.stringify(customerInfo));

    navigate(`/booking/payment-proof?${params.toString()}`);
  };

  if (!selectedService || !date || !time) {
    navigate('/');
    return null;
  }

  return (
    <BookingDetails 
      booking={booking}
      onBack={handleBack}
      onContinue={handleContinue}
    />
  );
}
