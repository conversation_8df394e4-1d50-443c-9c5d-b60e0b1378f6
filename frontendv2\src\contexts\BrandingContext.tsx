import { createContext, useContext, useState, useEffect, useCallback, type ReactNode } from 'react';
import { API_CONFIG } from '../utils/config';

interface BrandingContextType {
  branding: any;
  isLoading: boolean;
  error: string | null;
  refreshBranding: () => void;
  updateBranding: (newBranding: any) => void;
}

const BrandingContext = createContext<BrandingContextType>({
  branding: null,
  isLoading: true,
  error: null,
  refreshBranding: () => {},
  updateBranding: () => {},
});

interface BrandingProviderProps {
  children: ReactNode;
}

export const BrandingProvider = ({ children }: BrandingProviderProps) => {
  const [branding, setBranding] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load branding data from API with caching
  const loadBranding = useCallback(async (forceRefresh = false) => {
    try {
      setError(null);
      
      // Check cache first unless force refresh is requested
      if (!forceRefresh) {
        const cachedData = localStorage.getItem('branding_cache');
        const cacheTimestamp = localStorage.getItem('branding_cache_timestamp');
        
        if (cachedData && cacheTimestamp) {
          const cacheAge = Date.now() - parseInt(cacheTimestamp);
          // Use cache if less than 5 minutes old
          if (cacheAge < 5 * 60 * 1000) {
            console.log('BrandingContext: Using cached data');
            setBranding(JSON.parse(cachedData));
            setIsLoading(false);
            return;
          }
        }
      }

      console.log('BrandingContext: Fetching fresh data from API');
      const response = await fetch(`${API_CONFIG.BASE_URL}/branding`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success && data.data) {
        setBranding(data.data);
        
        // Cache the successful response
        localStorage.setItem('branding_cache', JSON.stringify(data.data));
        localStorage.setItem('branding_cache_timestamp', Date.now().toString());
        
        console.log('BrandingContext: Loaded fresh data from API');
      } else {
        throw new Error('No branding data received from API');
      }
    } catch (error: any) {
      console.error('Error loading branding:', error);
      setError(error.message || 'Failed to load branding configuration');
      setBranding(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load branding on mount
  useEffect(() => {
    loadBranding(false); // Use cache on initial load
  }, [loadBranding]);

  // Listen for localStorage changes to trigger immediate updates
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'branding_updated') {
        console.log('Branding update detected, refreshing...');
        loadBranding(true); // Force refresh
        localStorage.removeItem('branding_updated');
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Also check for changes in the same tab
    const checkForUpdates = () => {
      if (localStorage.getItem('branding_updated')) {
        console.log('Branding update detected (same tab), refreshing...');
        loadBranding(true);
        localStorage.removeItem('branding_updated');
      }
    };

    const updateCheckInterval = setInterval(checkForUpdates, 1000);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(updateCheckInterval);
    };
  }, [loadBranding]);

  // Refresh branding data manually (force refresh)
  const refreshBranding = useCallback(() => {
    loadBranding(true); // Force refresh from API
  }, [loadBranding]);

  // Update branding (for admin use) and refresh immediately
  const updateBranding = useCallback((newBranding: any) => {
    setBranding((prev: any) => ({ ...prev, ...newBranding }));
    // Clear cache and refresh from API to get latest data
    localStorage.removeItem('branding_cache');
    localStorage.removeItem('branding_cache_timestamp');
    setTimeout(() => {
      loadBranding(true); // Force refresh
    }, 1000);
  }, [loadBranding]);

  const value = {
    branding,
    isLoading,
    error,
    refreshBranding,
    updateBranding,
  };

  return (
    <BrandingContext.Provider value={value}>
      {children}
    </BrandingContext.Provider>
  );
};

// Custom hook to use branding context
export const useBranding = () => {
  const context = useContext(BrandingContext);
  if (!context) {
    throw new Error('useBranding must be used within a BrandingProvider');
  }
  return context;
};
