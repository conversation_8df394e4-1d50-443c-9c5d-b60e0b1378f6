// API service for backend communication

import { API_CONFIG } from './config';

export const API_BASE_URL = API_CONFIG.BASE_URL;

export interface User {
  _id?: string;
  id?: string;
  name?: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  role: 'user' | 'admin';
  isVerified?: boolean;
  favorites?: any[];
  notificationPreferences?: {
    email: boolean;
    sms: boolean;
    push: boolean;
  };
  createdAt: string;
  updatedAt?: string;
  __v?: number;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  data?: {
    user: User;
    token: string;
    refreshToken?: string;
  };
}

export interface RegisterResponse {
  success: boolean;
  message: string;
  data?: {
    user: User;
    token: string;
  };
}

export interface ApiError {
  success: false;
  message: string;
  errors?: any;
}

export interface TimeSlot {
  time: string;
  isAvailable: boolean;
}

export interface AdminAvailability {
  date: string;
  timeSlots: TimeSlot[];
  isFullDayUnavailable: boolean;
  reason?: string;
}

// Admin availability endpoints
export const availability = {
  // Get availability for date range (admin only)
  getAvailabilityRange: (startDate: string, endDate: string) =>
    apiRequest(`/admin/availability?startDate=${startDate}&endDate=${endDate}`),

  // Set availability for a date (admin only)
  setAvailability: (availability: AdminAvailability) =>
    apiRequest('/admin/availability', {
      method: 'POST',
      body: JSON.stringify(availability)
    }),

  // Set availability for multiple dates (admin only)
  setBulkAvailability: (data: { dates: string[]; timeSlots: TimeSlot[]; isFullDayUnavailable: boolean; reason?: string }) =>
    apiRequest('/admin/availability/bulk', {
      method: 'POST',
      body: JSON.stringify(data)
    }),

  // Delete availability for a date (admin only)
  deleteAvailability: (date: string) =>
    apiRequest(`/admin/availability/${date}`, {
      method: 'DELETE'
    }),

  // Delete availability for multiple dates (admin only)
  deleteBulkAvailability: (dates: string[]) =>
    apiRequest('/admin/availability', {
      method: 'DELETE',
      body: JSON.stringify({ dates })
    }),

  // Get global availability settings (admin only)
  getGlobalSettings: () =>
    apiRequest('/admin/availability/settings'),

  // Update global availability settings (admin only)
  updateGlobalSettings: (settings: any) =>
    apiRequest('/admin/availability/settings', {
      method: 'PUT',
      body: JSON.stringify(settings)
    }),

  // Get available time slots for a date (public)
  getTimeSlots: (date: string) =>
    apiRequest(`/availability/time-slots/${date}`),

  // Check if a specific time slot is available (public)
  checkAvailability: (date: string, time: string) =>
    apiRequest(`/availability/check?date=${date}&time=${time}`)
};

// Helper function to make API requests
const apiRequest = async (endpoint: string, options: RequestInit = {}): Promise<any> => {
  const url = `${API_BASE_URL}${endpoint}`;

  const defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // Add auth token if available
  const token = localStorage.getItem('authToken');
  console.log(`🔗 API Request: ${endpoint}, Token: ${token ? 'Present' : 'Missing'}`);

  if (token) {
    defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, config);

    // Check if response is ok first
    if (!response.ok) {
      let errorMessage = 'API request failed';
      try {
        const data = await response.json();
        errorMessage = data?.message || errorMessage;
      } catch (jsonError) {
        // If JSON parsing fails, use status text
        errorMessage = response.statusText || errorMessage;
      }

      // Handle authentication errors
      if (response.status === 401 || response.status === 403) {
        console.log('Authentication error detected - dispatching auth-error event');
        window.dispatchEvent(new CustomEvent('auth-error', {
          detail: {
            message: errorMessage,
            status: response.status
          }
        }));
      }

      throw new Error(errorMessage);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('API request error:', error);
    throw error;
  }
};

// Auth API functions
export const authAPI = {
  // Login user
  login: async (email: string, password: string): Promise<LoginResponse> => {
    const response = await apiRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });

    // Store token, refresh token and user data if login successful
    if (response.success && response.data?.token) {
      localStorage.setItem('authToken', response.data.token);
      if (response.data.refreshToken) {
        localStorage.setItem('refreshToken', response.data.refreshToken);
      }
      localStorage.setItem('currentUser', JSON.stringify(response.data.user));

      // Also store email and userId for easy access
      if (response.data.user?.email) {
        localStorage.setItem('userEmail', response.data.user.email);
      }
      if (response.data.user?._id || response.data.user?.id) {
        localStorage.setItem('userId', response.data.user._id || response.data.user.id);
      }
    }

    return response;
  },

  // Register user
  register: async (userData: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    password: string;
  }): Promise<RegisterResponse> => {
    const response = await apiRequest('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });

    // Store token and refresh token if registration successful
    if (response.success && response.data?.token) {
      localStorage.setItem('authToken', response.data.token);
      if (response.data.refreshToken) {
        localStorage.setItem('refreshToken', response.data.refreshToken);
      }
      localStorage.setItem('currentUser', JSON.stringify(response.data.user));

      // Also store email and userId for easy access
      if (response.data.user?.email) {
        localStorage.setItem('userEmail', response.data.user.email);
      }
      if (response.data.user?._id || response.data.user?.id) {
        localStorage.setItem('userId', response.data.user._id || response.data.user.id);
      }
    }

    return response;
  },

  // Logout user
  logout: async (): Promise<void> => {
    try {
      await apiRequest('/auth/logout', {
        method: 'POST',
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Always clear local storage
      clearAuthData();
    }
  },

  // Get current user info
  me: async (): Promise<{ success: boolean; data?: User }> => {
    return await apiRequest('/auth/me');
  },

  // Verify token
  verify: async (): Promise<{ success: boolean; data?: User }> => {
    return await apiRequest('/auth/verify');
  },

  // Check if email exists and get comprehensive user info
  checkEmail: async (email: string): Promise<{
    success: boolean;
    message: string;
    data: {
      exists: boolean;
      isAdmin: boolean;
      requiresPassword: boolean;
      user?: User;
      loginMethod: 'password' | 'email-only' | 'registration';
      message: string;
      authFlow: {
        nextStep: string;
        endpoint: string;
        description: string;
      };
    };
  }> => {
    return await apiRequest('/auth/check-email', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  },

  // Login with email only (for regular users)
  loginEmailOnly: async (email: string): Promise<LoginResponse> => {
    const response = await apiRequest('/auth/login-email-only', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });

    // Store token and refresh token if login successful
    if (response.success && response.data?.token) {
      localStorage.setItem('authToken', response.data.token);
      if (response.data.refreshToken) {
        localStorage.setItem('refreshToken', response.data.refreshToken);
      }
      localStorage.setItem('currentUser', JSON.stringify(response.data.user));

      // Also store email and userId for easy access
      if (response.data.user?.email) {
        localStorage.setItem('userEmail', response.data.user.email);
      }
      if (response.data.user?._id || response.data.user?.id) {
        localStorage.setItem('userId', response.data.user._id || response.data.user.id);
      }
    }

    return response;
  },

  // Check if email or phone exists and get comprehensive user info
  checkEmailOrPhone: async (emailOrPhone: string): Promise<{
    success: boolean;
    message: string;
    data: {
      exists: boolean;
      isAdmin: boolean;
      requiresPassword: boolean;
      user?: User;
      loginMethod: 'password' | 'email-only' | 'registration';
      message: string;
      authFlow: {
        nextStep: string;
        endpoint: string;
        description: string;
      };
    };
  }> => {
    const isEmail = emailOrPhone.includes('@');
    const endpoint = isEmail ? '/auth/check-email' : '/auth/check-phone';
    const body = isEmail ? { email: emailOrPhone } : { phone: emailOrPhone };

    return await apiRequest(endpoint, {
      method: 'POST',
      body: JSON.stringify(body),
    });
  },

  // Login with email or phone (for regular users)
  loginWithEmailOrPhone: async (emailOrPhone: string): Promise<LoginResponse> => {
    const isEmail = emailOrPhone.includes('@');
    const endpoint = isEmail ? '/auth/login-email-only' : '/auth/login-phone-only';
    const body = isEmail ? { email: emailOrPhone } : { phone: emailOrPhone };

    const response = await apiRequest(endpoint, {
      method: 'POST',
      body: JSON.stringify(body),
    });

    // Store token and refresh token if login successful
    if (response.success && response.data?.token) {
      localStorage.setItem('authToken', response.data.token);
      if (response.data.refreshToken) {
        localStorage.setItem('refreshToken', response.data.refreshToken);
      }
      localStorage.setItem('currentUser', JSON.stringify(response.data.user));

      // Also store email and userId for easy access
      if (response.data.user?.email) {
        localStorage.setItem('userEmail', response.data.user.email);
      }
      if (response.data.user?._id || response.data.user?.id) {
        localStorage.setItem('userId', response.data.user._id || response.data.user.id);
      }
    }

    return response;
  },
};

// Utility functions for local auth state
export const getCurrentUser = (): User | null => {
  const user = localStorage.getItem('currentUser');
  return user ? JSON.parse(user) : null;
};

export const isAuthenticated = (): boolean => {
  return !!localStorage.getItem('authToken');
};

export const isAdmin = (): boolean => {
  const user = getCurrentUser();
  return user?.role === 'admin';
};

export const clearAuthData = (): void => {
  localStorage.removeItem('authToken');
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('currentUser');
  localStorage.removeItem('userEmail');
  localStorage.removeItem('userId');
};

// Helper function to save user data to localStorage
export const saveUserData = (user: any, token?: string, refreshToken?: string): void => {
  if (token) {
    localStorage.setItem('authToken', token);
  }
  if (refreshToken) {
    localStorage.setItem('refreshToken', refreshToken);
  }
  localStorage.setItem('currentUser', JSON.stringify(user));

  // Also store email and userId for easy access
  if (user?.email) {
    localStorage.setItem('userEmail', user.email);
  }
  if (user?._id || user?.id) {
    localStorage.setItem('userId', user._id || user.id);
  }
};

// Loading states management
export const createLoadingState = () => {
  let isLoading = false;
  let error: string | null = null;

  return {
    setLoading: (loading: boolean) => { isLoading = loading; },
    setError: (err: string | null) => { error = err; },
    getLoading: () => isLoading,
    getError: () => error,
    clearError: () => { error = null; },
  };
};

// Error handling utility
export const handleApiError = (error: any): string => {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unexpected error occurred';
};

// Helper function to get stored user email and ID
export const getStoredUserData = (): { email?: string; userId?: string } => {
  return {
    email: localStorage.getItem('userEmail') || undefined,
    userId: localStorage.getItem('userId') || undefined
  };
};

// Helper function to save login response data (for the specific format you provided)
export const saveLoginResponseData = (loginResponse: {
  success: boolean;
  message: string;
  data: {
    user: any;
    token: string;
    refreshToken: string;
  };
}): void => {
  if (loginResponse.success && loginResponse.data) {
    const { user, token, refreshToken } = loginResponse.data;

    // Save tokens
    localStorage.setItem('authToken', token);
    localStorage.setItem('refreshToken', refreshToken);

    // Save user data
    localStorage.setItem('currentUser', JSON.stringify(user));

    // Save email and userId for easy access
    if (user.email) {
      localStorage.setItem('userEmail', user.email);
    }
    if (user._id) {
      localStorage.setItem('userId', user._id);
    }

    console.log('Login data saved successfully:', {
      userId: user._id,
      email: user.email,
      name: user.name,
      role: user.role
    });
  }
};

// Dashboard data interfaces
export interface DashboardAppointment {
  id: string;
  serviceId: string;
  serviceName: string;
  servicePrice: number;
  serviceDuration?: number;
  serviceCategory?: string;
  date: string;
  time: string;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  customerInfo: {
    name: string;
    email: string;
    phone: string;
  };
  notes?: string;
  paymentProofs?: Array<{
    id: string;
    amount: number;
    paymentMethod: string;
    proofImage: string;
    status: 'pending' | 'verified' | 'rejected';
    notes?: string;
    createdAt: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface DashboardStatistics {
  appointments: {
    total: number;
    pending: number;
    confirmed: number;
    completed: number;
    cancelled: number;
  };
  totalSpent: number;
  favoriteServices: Array<{
    id: string;
    name: string;
    count: number;
    price: number;
    category: string;
  }>;
  memberSince: string;
  lastActivity: string;
}

export interface DashboardData {
  user: User;
  appointments: {
    all: DashboardAppointment[];
    recent: DashboardAppointment[];
    upcoming: DashboardAppointment[];
  };
  statistics: DashboardStatistics;
  userReviews?: any[];
  completedAppointmentsToReview?: any[];
}

export interface DashboardResponse {
  success: boolean;
  message: string;
  data: DashboardData;
}

// Dashboard API
export const dashboardAPI = {
  // Get user dashboard data using stored authentication (preferred method)
  getUserDashboard: async (): Promise<DashboardResponse> => {
    const token = localStorage.getItem('authToken');

    if (!token) {
      // Fallback to public endpoint if no token
      return dashboardAPI.getUserDashboardPublic();
    }

    // Use authenticated dashboard endpoint
    return await apiRequest('/dashboard', {
      method: 'GET',
    });
  },

  // Get user dashboard data using email/userId (public endpoint fallback)
  getUserDashboardPublic: async (): Promise<DashboardResponse> => {
    const { email, userId } = getStoredUserData();

    if (!email && !userId) {
      throw new Error('No user data found in localStorage');
    }

    const params = new URLSearchParams();
    if (userId) {
      params.set('userId', userId);
    } else if (email) {
      params.set('email', email);
    }

    // Use the public dashboard endpoint
    return await apiRequest(`/dashboard/user?${params.toString()}`, {
      method: 'GET',
    });
  },

  // Get user appointments using stored email/userId (fallback method)
  getUserAppointments: async (): Promise<any> => {
    const { email, userId } = getStoredUserData();

    if (!email && !userId) {
      throw new Error('No user data found in localStorage');
    }

    const params = new URLSearchParams();
    if (userId) {
      params.set('userId', userId);
    } else if (email) {
      params.set('email', email);
    }

    return await apiRequest(`/appointments/user?${params.toString()}`, {
      method: 'GET',
    });
  }
};

// Admin appointment interfaces
export interface AdminAppointment {
  id: string;
  user?: {
    id: string;
    name: string;
    email: string;
    phone: string;
  };
  customerInfo: {
    name: string;
    email: string;
    phone: string;
  };
  service: {
    id: string;
    name: string;
    price: number;
    duration: number;
    category: string;
  } | null;
  date: string;
  time: string;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  message?: string;
  totalPrice?: number;
  paymentProofs?: Array<{
    id: string;
    amount: number;
    paymentMethod: string;
    proofImage: string;
    status: 'pending' | 'verified' | 'rejected';
    notes?: string;
    createdAt: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface AppointmentFilters {
  page?: number;
  limit?: number;
  status?: string;
  date?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface AppointmentListResponse {
  success: boolean;
  message: string;
  data: {
    appointments: AdminAppointment[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

export interface AppointmentAnalytics {
  statusStats: {
    pending?: number;
    confirmed?: number;
    completed?: number;
    cancelled?: number;
  };
  dailyStats: Array<{
    _id: string;
    count: number;
    confirmed: number;
    completed: number;
    cancelled: number;
  }>;
  serviceStats: Array<{
    _id: string;
    serviceName: string;
    count: number;
    revenue: number;
  }>;
  revenueStats: {
    totalRevenue: number;
    averageRevenue: number;
    totalAppointments: number;
  };
  todayAppointments: number;
  totalAppointments: number;
}

// Customer interfaces
export interface Customer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  totalAppointments: number;
  totalSpent: number;
  lastAppointment?: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export interface CustomerDetails extends Customer {
  isVerified: boolean;
  notificationPreferences: {
    email: boolean;
    sms: boolean;
    push: boolean;
  };
  statistics: {
    totalAppointments: number;
    totalSpent: number;
    lastAppointment?: string;
  };
  recentAppointments: Array<{
    id: string;
    service: {
      id: string;
      name: string;
      price: number;
    } | null;
    date: string;
    time: string;
    status: string;
    totalPrice: number;
  }>;
}

export interface CustomerListResponse {
  success: boolean;
  data: {
    customers: Customer[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

export interface CustomerFilters {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Admin API functions
export const adminAPI = {
  // Get all appointments with filtering and pagination
  getAppointments: async (filters: AppointmentFilters = {}): Promise<AppointmentListResponse> => {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.set(key, value.toString());
      }
    });

    return await apiRequest(`/admin/appointments?${params.toString()}`);
  },

  // Get single appointment details
  getAppointment: async (id: string): Promise<{ success: boolean; data: AdminAppointment }> => {
    return await apiRequest(`/admin/appointments/${id}`);
  },

  // Create new appointment
  createAppointment: async (appointmentData: {
    userId?: string;
    service: string;
    date: string;
    time: string;
    customerInfo: {
      name: string;
      email: string;
      phone: string;
    };
    message?: string;
  }): Promise<{ success: boolean; data: AdminAppointment }> => {
    return await apiRequest('/admin/appointments', {
      method: 'POST',
      body: JSON.stringify(appointmentData),
    });
  },

  // Update appointment
  updateAppointment: async (id: string, updateData: {
    date?: string;
    time?: string;
    customerInfo?: Partial<{
      name: string;
      email: string;
      phone: string;
    }>;
    message?: string;
    status?: string;
  }): Promise<{ success: boolean; data: AdminAppointment }> => {
    return await apiRequest(`/admin/appointments/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  },

  // Update appointment status
  updateAppointmentStatus: async (id: string, status: string): Promise<{ success: boolean; data: any }> => {
    return await apiRequest(`/admin/appointments/${id}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    });
  },

  // Delete appointment
  deleteAppointment: async (id: string): Promise<{ success: boolean; data: { id: string } }> => {
    return await apiRequest(`/admin/appointments/${id}`, {
      method: 'DELETE',
    });
  },

  // Bulk update appointment status
  bulkUpdateStatus: async (appointmentIds: string[], status: string): Promise<{ success: boolean; data: any }> => {
    return await apiRequest('/admin/appointments/bulk-status', {
      method: 'PUT',
      body: JSON.stringify({ appointmentIds, status }),
    });
  },

  // Get appointment analytics
  getAnalytics: async (startDate?: string, endDate?: string): Promise<{ success: boolean; data: AppointmentAnalytics }> => {
    const params = new URLSearchParams();
    if (startDate) params.set('startDate', startDate);
    if (endDate) params.set('endDate', endDate);

    return await apiRequest(`/admin/appointments/analytics?${params.toString()}`);
  },

  // Get dashboard overview
  getDashboard: async (): Promise<{ success: boolean; data: any }> => {
    return await apiRequest('/admin/dashboard');
  },

  // Get customers/users
  getCustomers: async (filters: { page?: number; limit?: number; search?: string } = {}): Promise<{ success: boolean; data: any }> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.set(key, value.toString());
      }
    });
    return await apiRequest(`/admin/customers?${params.toString()}`);
  },

  // Get services
  getServices: async (filters: { page?: number; limit?: number; search?: string; category?: string } = {}): Promise<{ success: boolean; data: any }> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.set(key, value.toString());
      }
    });
    return await apiRequest(`/admin/services?${params.toString()}`);
  },
  // Create service
  createService: async (serviceData: any): Promise<{ success: boolean; data: any }> => {
    return await apiRequest('/admin/services', {
      method: 'POST',
      body: JSON.stringify(serviceData)
    });
  },

  // Update service
  updateService: async (serviceId: string, serviceData: any): Promise<{ success: boolean; data: any }> => {
    return await apiRequest(`/admin/services/${serviceId}`, {
      method: 'PUT',
      body: JSON.stringify(serviceData)
    });
  },

  // Delete service
  deleteService: async (serviceId: string): Promise<{ success: boolean; data: any }> => {
    return await apiRequest(`/admin/services/${serviceId}`, {
      method: 'DELETE'
    });
  },

  // Get settings/branding
  getSettings: async (): Promise<{ success: boolean; data: any }> => {
    return await apiRequest('/admin/settings');
  },

  // Update settings/branding
  updateSettings: async (settingsData: any): Promise<{ success: boolean; data: any }> => {
    return await apiRequest('/admin/settings', {
      method: 'PUT',
      body: JSON.stringify(settingsData)
    });
  },

  // Review management
  getReviews: async (filters: any = {}): Promise<any> => {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value));
      }
    });

    const queryString = params.toString();
    const endpoint = `/admin/reviews${queryString ? `?${queryString}` : ''}`;

    return apiRequest(endpoint, {
      method: 'GET'
    });
  },

  updateReviewStatus: async (reviewId: string, status: string, adminNote?: string): Promise<any> => {
    return apiRequest(`/admin/reviews/${reviewId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status, adminNote })
    });
  },

  updateReview: async (reviewId: string, updateData: any): Promise<any> => {
    return apiRequest(`/admin/reviews/${reviewId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData)
    });
  },

  deleteReview: async (reviewId: string): Promise<any> => {
    return apiRequest(`/admin/reviews/${reviewId}`, {
      method: 'DELETE'
    });
  }
};
