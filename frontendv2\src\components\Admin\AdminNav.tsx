import { NavLink } from 'react-router-dom';

export default function AdminNav() {
  const navLinkStyle = {
    padding: '0.75rem 1rem',
    textDecoration: 'none',
    fontSize: 'clamp(0.7rem, 2vw, 0.875rem)',
    fontWeight: '500',
    borderRadius: '0.5rem',
    transition: 'all 0.2s',
    whiteSpace: 'nowrap' as const,
    display: 'inline-block'
  };

  const activeStyle = {
    ...navLinkStyle,
    backgroundColor: '#3b82f6',
    color: 'white'
  };

  const inactiveStyle = {
    ...navLinkStyle,
    backgroundColor: 'transparent',
    color: '#64748b'
  };

  return (
    <nav className="admin-nav" style={{
      display: 'flex',
      gap: '0.5rem',
      padding: '0.5rem 0',
      overflowX: 'auto'
    }}>
      <NavLink
        to="/admin"
        end
        style={({ isActive }) => isActive ? activeStyle : inactiveStyle}
      >
        📊 DASHBOARD
      </NavLink>
      <NavLink
        to="/admin/appointments"
        style={({ isActive }) => isActive ? activeStyle : inactiveStyle}
      >
        📅 APPOINTMENTS
      </NavLink>
      <NavLink
        to="/admin/calendar"
        style={({ isActive }) => isActive ? activeStyle : inactiveStyle}
      >
        🗓️ AVAILABILITY
      </NavLink>
      <NavLink
        to="/admin/customers"
        style={({ isActive }) => isActive ? activeStyle : inactiveStyle}
      >
        👥 CUSTOMERS
      </NavLink>
      <NavLink
        to="/admin/services"
        style={({ isActive }) => isActive ? activeStyle : inactiveStyle}
      >
        💇‍♀️ SERVICES
      </NavLink>
      <NavLink
        to="/admin/reviews"
        style={({ isActive }) => isActive ? activeStyle : inactiveStyle}
      >
        ⭐ REVIEWS
      </NavLink>
    </nav>
  );
}
