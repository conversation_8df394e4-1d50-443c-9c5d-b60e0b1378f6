import { useBranding } from '../../contexts/BrandingContext';

export default function DisclaimerSection() {
  const { branding: brandingData } = useBranding();

  const disclaimer = brandingData?.home?.disclaimer;

  // Check if section is enabled (default to true if not specified)
  const isEnabled = brandingData?.home?.sectionVisibility?.disclaimer !== false;

  if (!isEnabled) {
    return null;
  }

  return (
    <div className="disclaimer-section">
      <div className="disclaimer-card">
        <h3 className="disclaimer-title">
          {disclaimer?.title || 'Disclaimer'}
        </h3>
        <div className="disclaimer-content">
          {disclaimer?.content ? (
            <div dangerouslySetInnerHTML={{ __html: disclaimer.content }} />
          ) : (
            <>
              <p>By booking and receiving services, you acknowledge that all hairstyling and haircare services are provided at your request. Results may vary depending on your hair's condition, texture, and aftercare.</p>
              <p>The service provider and its stylists are not responsible for dissatisfaction, perceived damages, or outcomes outside of what was discussed and agreed upon prior to service. By proceeding with an appointment, you waive the right to pursue legal action against the service provider or its affiliates in connection with services rendered.</p>
              <p>All payments, sales, and services are final.</p>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
