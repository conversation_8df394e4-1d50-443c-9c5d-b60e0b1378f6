import { useBranding } from '../../contexts/BrandingContext';

export default function HeroSection() {
  const { branding: brandingData } = useBranding();

  return (
    <>
      {/* Profile Section */}
      <div className="profile-section">
        <div className="profile-image">
          <div className="profile-avatar">
            <div className="avatar-placeholder">
              {brandingData?.global?.siteName ?
                brandingData.global.siteName.split(' ').map((word: string) => word.charAt(0)).join('').slice(0, 3) :
                'DSB'
              }
            </div>
          </div>
        </div>
        <h1 className="business-name">
          {brandingData?.home?.businessName || brandingData?.global?.siteName || 'dammyspicybeauty'}
        </h1>
      </div>

      {/* Hero Banner */}
      <div className="hero-banner">
        <div className="hero-content">
          <div className="hero-text">
            <h2 className="hero-title">
              {brandingData?.home?.heroTitle || brandingData?.global?.siteName || 'dammyspicybeauty'}
            </h2>
            <p className="location">
              {brandingData?.home?.location || brandingData?.global?.address || 'INDIANAPOLIS, IN'}
            </p>
            <p className="welcome-text">
              {brandingData?.home?.welcomeText || 'Welcome to my booking site!'}
            </p>
          </div>
          <div className="hero-image">
            <div className="stylist-image">
              {brandingData?.home?.heroImage ? (
                <img
                  src={brandingData.home.heroImage}
                  alt="Professional stylist"
                  className="hero-image-actual"
                />
              ) : (
                <div className="stylist-placeholder">
                  <div className="stylist-silhouette"></div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
