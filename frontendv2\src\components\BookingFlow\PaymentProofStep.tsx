import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { API_CONFIG } from '../../utils/config';
import { useToast } from '../../contexts/ToastContext';

interface PaymentProofStepProps {
  onBack: () => void;
}

export default function PaymentProofStep({ onBack }: PaymentProofStepProps) {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { showSuccess, showError } = useToast();
  
  const [paymentMethod, setPaymentMethod] = useState<'cashapp' | 'zelle'>('cashapp');
  const [paymentProof, setPaymentProof] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);

  // Get booking data from URL params
  const serviceParam = searchParams.get('service');
  const addonsParam = searchParams.get('addons');
  const date = searchParams.get('date');
  const time = searchParams.get('time');
  const customerInfoParam = searchParams.get('customerInfo');
  
  let selectedService = null;
  let selectedAddOns: any[] = [];
  let customerInfo = null;
  
  if (serviceParam) {
    try {
      selectedService = JSON.parse(serviceParam);
    } catch (error) {
      console.error('Error parsing service:', error);
    }
  }
  
  if (addonsParam) {
    try {
      selectedAddOns = JSON.parse(addonsParam);
    } catch (error) {
      console.error('Error parsing addons:', error);
    }
  }
  
  if (customerInfoParam) {
    try {
      customerInfo = JSON.parse(customerInfoParam);
    } catch (error) {
      console.error('Error parsing customer info:', error);
    }
  }

  const calculateTotal = () => {
    const servicePrice = parseFloat(selectedService?.price || '0');
    const addOnsTotal = selectedAddOns.reduce((sum, addon) => sum + (addon.price || 0), 0);
    return servicePrice + addOnsTotal;
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Check file size (10MB limit)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        showError('File is too large. Please select a file smaller than 10MB.');
        return;
      }
      setPaymentProof(file);
    }
  };

  const handleUploadPayment = async () => {
    if (!paymentProof || !customerInfo?.email) {
      showError('Please select a payment proof file and ensure customer info is complete.');
      return;
    }

    setIsUploading(true);
    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', paymentProof);

      // Add email and appointment data to form
      formData.append('email', customerInfo.email);
      formData.append('appointmentData', JSON.stringify({
        selectedService,
        selectedAddOns,
        date,
        time,
        customerInfo,
        paymentMethod,
        paymentAmount: calculateTotal(),
        userId: customerInfo?.userId
      }));

      console.log('Uploading payment proof:', paymentProof.name, 'Size:', (paymentProof.size / 1024 / 1024).toFixed(2) + 'MB');

      // Upload to our backend endpoint
      const response = await fetch(`${API_CONFIG.BASE_URL.replace('/v2', '')}/upload/payment-proof`, {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        const errorMessage = data.message || 'Failed to upload payment proof';
        throw new Error(errorMessage);
      }

      console.log('Payment proof uploaded:', data.data?.url);

      // Save payment proof URL to localStorage for appointment creation
      if (data.data?.url) {
        localStorage.setItem('paymentProofUrl', data.data.url);
        localStorage.setItem('paymentProofData', JSON.stringify({
          url: data.data.url,
          amount: calculateTotal(),
          paymentMethod: paymentMethod,
          notes: `Payment proof uploaded via ${paymentMethod}`,
          uploadedAt: data.data.uploadedAt
        }));
        console.log('Payment proof data saved to localStorage');
      }

      setUploadSuccess(true);
      showSuccess('Payment proof uploaded successfully!');

      // Navigate directly to success page with appointment data
      setTimeout(() => {
        navigate('/appointment-success', {
          state: {
            appointmentData: {
              id: data.data?.appointmentId || 'temp-id',
              userId: customerInfo?.userId || '',
              serviceId: selectedService.id,
              serviceName: selectedService.name,
              servicePrice: parseFloat(selectedService.price),
              date: date,
              time: time,
              status: 'confirmed',
              customerInfo: {
                name: `${customerInfo.firstName} ${customerInfo.lastName}`,
                email: customerInfo.email,
                phone: customerInfo.phone
              },
              totalPrice: calculateTotal(),
              addOns: selectedAddOns,
              paymentProofs: [{
                id: 'temp-proof-id',
                amount: calculateTotal(),
                paymentMethod: paymentMethod,
                proofImage: data.data?.url || '',
                status: 'uploaded',
                notes: `Payment proof uploaded via ${paymentMethod}`,
                createdAt: data.data?.uploadedAt || new Date().toISOString()
              }],
              paymentStatus: 'proof_uploaded',
              createdAt: data.data?.uploadedAt || new Date().toISOString()
            }
          }
        });
      }, 1500);

    } catch (error) {
      console.error('Payment proof upload error:', error);
      showError(error instanceof Error ? error.message : 'Failed to upload payment proof');
    } finally {
      setIsUploading(false);
    }
  };

  // Handle navigation when required data is missing
  useEffect(() => {
    if (!selectedService || !date || !time || !customerInfo) {
      const timer = setTimeout(() => {
        navigate('/');
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [selectedService, date, time, customerInfo, navigate]);

  if (!selectedService || !date || !time || !customerInfo) {
    return (
      <div className="app">
        <main className="main-content">
          <div className="payment-proof-step">
            <div className="loading-state">Loading payment information...</div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="app">
      <div className="booking-container">
        <div className="booking-header">
          <button onClick={onBack} className="back-button">
            ← Back
          </button>
          <h1>Payment Proof Upload</h1>
        </div>

        <div className="booking-content">
          <div className="payment-proof-section">
            <h2>Upload Payment Proof</h2>
            <p>Please upload proof of your payment to complete your booking.</p>

            <div className="booking-summary">
              <h3>Booking Summary</h3>
              <div className="summary-item">
                <span>Service:</span>
                <span>{selectedService.name}</span>
              </div>
              <div className="summary-item">
                <span>Date:</span>
                <span>{date}</span>
              </div>
              <div className="summary-item">
                <span>Time:</span>
                <span>{time}</span>
              </div>
              <div className="summary-item total">
                <span>Total Amount:</span>
                <span>${calculateTotal().toFixed(2)}</span>
              </div>
            </div>

            <div className="payment-method-section">
              <h3>Payment Method</h3>
              <div className="payment-methods">
                <label className={`payment-method ${paymentMethod === 'cashapp' ? 'selected' : ''}`}>
                  <input
                    type="radio"
                    value="cashapp"
                    checked={paymentMethod === 'cashapp'}
                    onChange={(e) => setPaymentMethod(e.target.value as 'cashapp')}
                  />
                  <span>CashApp</span>
                </label>
                <label className={`payment-method ${paymentMethod === 'zelle' ? 'selected' : ''}`}>
                  <input
                    type="radio"
                    value="zelle"
                    checked={paymentMethod === 'zelle'}
                    onChange={(e) => setPaymentMethod(e.target.value as 'zelle')}
                  />
                  <span>Zelle</span>
                </label>
              </div>
            </div>

            <div className="file-upload-section">
              <h3>Upload Payment Proof</h3>
              <div className="file-upload-wrapper">
                <input
                  type="file"
                  id="payment-proof"
                  accept="image/*,.pdf"
                  onChange={handleFileUpload}
                  className="file-input"
                  style={{ display: 'none' }}
                />
                <label htmlFor="payment-proof" className="file-upload-label">
                  {paymentProof ? paymentProof.name : 'Choose File (Image or PDF)'}
                </label>
              </div>

              <button
                className="upload-button"
                onClick={handleUploadPayment}
                disabled={!paymentProof || isUploading || uploadSuccess}
                style={{
                  backgroundColor: uploadSuccess ? '#10b981' : isUploading ? '#6b7280' : '#000000',
                  color: 'white',
                  padding: '12px 24px',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '16px',
                  fontWeight: '600',
                  cursor: (!paymentProof || isUploading || uploadSuccess) ? 'not-allowed' : 'pointer',
                  opacity: (!paymentProof || isUploading || uploadSuccess) ? 0.6 : 1,
                  transition: 'all 0.2s ease',
                  marginTop: '16px',
                  width: '100%'
                }}
              >
                {isUploading ? (
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
                    <div style={{
                      width: '16px',
                      height: '16px',
                      border: '2px solid #ffffff',
                      borderTop: '2px solid transparent',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }}></div>
                    UPLOADING...
                  </div>
                ) : uploadSuccess ? (
                  'UPLOADED SUCCESSFULLY ✓'
                ) : (
                  'UPLOAD PAYMENT PROOF'
                )}
              </button>
            </div>

            {uploadSuccess && (
              <div style={{
                backgroundColor: '#d1fae5',
                border: '1px solid #10b981',
                borderRadius: '8px',
                padding: '12px',
                marginTop: '16px',
                color: '#065f46',
                textAlign: 'center' as const
              }}>
                Payment proof uploaded successfully! Proceeding to complete your booking...
              </div>
            )}
          </div>
        </div>
      </div>

      <style>{`
        .payment-proof-section {
          max-width: 600px;
          margin: 0 auto;
          padding: 24px;
        }

        .booking-summary {
          background: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          padding: 16px;
          margin: 24px 0;
        }

        .summary-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
        }

        .summary-item.total {
          font-weight: 600;
          border-top: 1px solid #e5e7eb;
          padding-top: 8px;
          margin-top: 8px;
        }

        .payment-method-section {
          margin: 24px 0;
        }

        .payment-methods {
          display: flex;
          gap: 16px;
          margin-top: 12px;
        }

        .payment-method {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 12px 16px;
          border: 2px solid #e5e7eb;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .payment-method.selected {
          border-color: #8b5cf6;
          background-color: #f3f4f6;
        }

        .payment-method input {
          margin: 0;
        }

        .file-upload-section {
          margin: 24px 0;
        }

        .file-upload-wrapper {
          margin: 12px 0;
        }

        .file-upload-label {
          display: block;
          padding: 12px 16px;
          border: 2px dashed #d1d5db;
          border-radius: 8px;
          text-align: center;
          cursor: pointer;
          transition: all 0.2s ease;
          background-color: #f9fafb;
        }

        .file-upload-label:hover {
          border-color: #8b5cf6;
          background-color: #f3f4f6;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
