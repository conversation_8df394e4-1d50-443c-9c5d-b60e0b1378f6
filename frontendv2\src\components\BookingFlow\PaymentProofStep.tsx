import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { appointmentAPI } from '../../utils/appointmentAPI';
import { useNotifications } from '../../hooks/useRealTimeUpdates';
import { API_CONFIG } from '../../utils/config';
import { useToast } from '../../contexts/ToastContext';

interface PaymentProofStepProps {
  onBack: () => void;
}

export default function PaymentProofStep({ onBack }: PaymentProofStepProps) {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { showNotification } = useNotifications();
  const { showSuccess, showError } = useToast();
  
  const [paymentMethod, setPaymentMethod] = useState<'cashapp' | 'zelle'>('cashapp');
  const [paymentProof, setPaymentProof] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);

  // Get booking data from URL params
  const serviceParam = searchParams.get('service');
  const addonsParam = searchParams.get('addons');
  const date = searchParams.get('date');
  const time = searchParams.get('time');
  const customerInfoParam = searchParams.get('customerInfo');
  
  let selectedService = null;
  let selectedAddOns: any[] = [];
  let customerInfo = null;
  
  if (serviceParam) {
    try {
      selectedService = JSON.parse(serviceParam);
    } catch (error) {
      console.error('Error parsing service:', error);
    }
  }
  
  if (addonsParam) {
    try {
      selectedAddOns = JSON.parse(addonsParam);
    } catch (error) {
      console.error('Error parsing addons:', error);
    }
  }
  
  if (customerInfoParam) {
    try {
      customerInfo = JSON.parse(customerInfoParam);
    } catch (error) {
      console.error('Error parsing customer info:', error);
    }
  }

  const calculateTotal = () => {
    const servicePrice = parseFloat(selectedService?.price || '0');
    const addOnsTotal = selectedAddOns.reduce((sum, addon) => sum + (addon.price || 0), 0);
    return servicePrice + addOnsTotal;
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {

      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        showError('File is too large. Please select a file smaller than 10MB.');
        return;
      }
      setPaymentProof(file);
    }
  };

  const handleUploadPayment = async () => {
    if (!paymentProof || !customerInfo?.email) {
      showError('Please select a payment proof file and ensure customer info is complete.');
      return;
    }

    setIsUploading(true);
    try {

      const formData = new FormData();
      formData.append('file', paymentProof);

      formData.append('email', customerInfo.email);
      formData.append('appointmentData', JSON.stringify({
        selectedService,
        selectedAddOns,
        date,
        time,
        customerInfo,
        paymentMethod,
        paymentAmount: calculateTotal(),
        userId: customerInfo?.userId
      }));

      console.log('Uploading payment proof:', paymentProof.name, 'Size:', (paymentProof.size / 1024 / 1024).toFixed(2) + 'MB');

      const response = await fetch(`${API_CONFIG.BASE_URL.replace('/v2', '')}/upload/payment-proof`, {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        const errorMessage = data.message || 'Failed to upload payment proof';
        throw new Error(errorMessage);
      }

      console.log('Payment proof uploaded:', data.data?.url);

      if (data.data?.url) {
        localStorage.setItem('paymentProofUrl', data.data.url);
        localStorage.setItem('paymentProofData', JSON.stringify({
          url: data.data.url,
          amount: calculateTotal(),
          paymentMethod: paymentMethod,
          notes: `Payment proof uploaded via ${paymentMethod}`,
          uploadedAt: data.data.uploadedAt
        }));
        console.log('Payment proof data saved to localStorage');
      }

      setUploadSuccess(true);
      showSuccess('Payment proof uploaded successfully!');

      localStorage.setItem('paymentProofData', JSON.stringify({
        url: data.data?.url,
        amount: calculateTotal(),
        paymentMethod: paymentMethod,
        notes: `Payment proof uploaded via ${paymentMethod}`,
        uploadedAt: data.data?.uploadedAt || new Date().toISOString()
      }));

      try {
        const appointmentData = {
          serviceId: (selectedService?.id || selectedService?._id)?.toString() || '',
          serviceName: selectedService?.name || '',
          servicePrice: parseFloat(selectedService?.price || '0'),
          addOns: selectedAddOns || [],
          date: date || '',
          time: time || '',
          customerInfo: {
            firstName: customerInfo?.firstName || '',
            lastName: customerInfo?.lastName || '',
            email: customerInfo?.email || '',
            phone: customerInfo?.phone || '',
            paymentMethod: paymentMethod as 'cashapp' | 'zelle',
            paymentDetails: `Payment proof uploaded: ${data.data?.fileName || 'file'}`
          },
          totalPrice: calculateTotal()
        };

        console.log('Creating appointment with data:', appointmentData);
        const appointmentResponse = await appointmentAPI.createAppointment(appointmentData);
        console.log('Appointment created successfully:', appointmentResponse);

        // Save appointment data to localStorage for success page
        localStorage.setItem('appointmentSuccessData', JSON.stringify(appointmentResponse));
        console.log('Saved to localStorage:', appointmentResponse);

        showNotification('Payment proof uploaded successfully! Your appointment has been booked and is pending verification.', 'success');

        setTimeout(() => {
          navigate('/appointment-success');
        }, 1500);

        // Clean up payment proof localStorage
        localStorage.removeItem('paymentProofData');

      } catch (appointmentError) {
        console.error('Error creating appointment:', appointmentError);

        let errorMessage = 'Payment proof uploaded but failed to create appointment. Please contact support.';
        if (appointmentError instanceof Error && appointmentError.message) {
          errorMessage = appointmentError.message;
        }

        showNotification(errorMessage, 'error');

        // Save error state to localStorage for success page
        localStorage.setItem('appointmentSuccessData', JSON.stringify({
          id: 'temp-id',
          error: true,
          errorMessage: errorMessage,
          paymentProofUploaded: true,
          paymentProofUrl: data.data?.url
        }));

        setTimeout(() => {
          navigate('/appointment-success');
        }, 1500);
      }

    } catch (error) {
      console.error('Payment proof upload error:', error);

      let errorMessage = 'Failed to upload payment proof. Please try again.';
      if (error instanceof Error && error.message) {
        if (error.message.includes('timeout') || error.message.includes('Timeout')) {
          errorMessage = 'Upload timed out. Please check your internet connection and try again with a smaller file.';
        } else if (error.message.includes('too large')) {
          errorMessage = 'File is too large. Please use a file smaller than 10MB.';
        } else if (error.message.includes('Invalid file type')) {
          errorMessage = 'Invalid file type. Please use JPEG, PNG, GIF, WebP, or PDF files only.';
        } else {
          errorMessage = error.message;
        }
      }

      showNotification(errorMessage, 'error');
    } finally {
      setIsUploading(false);
    }
  };

  useEffect(() => {
    if (!selectedService || !date || !time || !customerInfo) {
      const timer = setTimeout(() => {
        navigate('/');
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [selectedService, date, time, customerInfo, navigate]);

  if (!selectedService || !date || !time || !customerInfo) {
    return (
      <div className="app">
        <main className="main-content">
          <div className="payment-proof-step">
            <div className="loading-state">Loading payment information...</div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="app">
      <div className="booking-container">
        <div className="booking-header">
          <button onClick={onBack} className="back-button">
            ← Back
          </button>
          <h1>Payment Proof Upload</h1>
        </div>

        <div className="booking-content">
          <div className="payment-proof-section">
            <h2>Upload Payment Proof</h2>
            <p>Please upload proof of your payment to complete your booking.</p>

            <div className="booking-summary">
              <h3>Booking Summary</h3>
              <div className="summary-item">
                <span>Service:</span>
                <span>{selectedService.name}</span>
              </div>
              <div className="summary-item">
                <span>Date:</span>
                <span>{date}</span>
              </div>
              <div className="summary-item">
                <span>Time:</span>
                <span>{time}</span>
              </div>
              <div className="summary-item total">
                <span>Total Amount:</span>
                <span>${calculateTotal().toFixed(2)}</span>
              </div>
            </div>

            <div className="payment-method-section">
              <h3>Payment Method</h3>
              <div className="payment-methods">
                <label className={`payment-method ${paymentMethod === 'cashapp' ? 'selected' : ''}`}>
                  <input
                    type="radio"
                    value="cashapp"
                    checked={paymentMethod === 'cashapp'}
                    onChange={(e) => setPaymentMethod(e.target.value as 'cashapp')}
                  />
                  <span>CashApp</span>
                </label>
                <label className={`payment-method ${paymentMethod === 'zelle' ? 'selected' : ''}`}>
                  <input
                    type="radio"
                    value="zelle"
                    checked={paymentMethod === 'zelle'}
                    onChange={(e) => setPaymentMethod(e.target.value as 'zelle')}
                  />
                  <span>Zelle</span>
                </label>
              </div>
            </div>

            <div className="file-upload-section">
              <h3>Upload Payment Proof</h3>
              <div className="file-upload-wrapper">
                <input
                  type="file"
                  id="payment-proof"
                  accept="image/*,.pdf"
                  onChange={handleFileUpload}
                  className="file-input"
                  style={{ display: 'none' }}
                />
                <label htmlFor="payment-proof" className="file-upload-label">
                  {paymentProof ? paymentProof.name : 'Choose File (Image or PDF)'}
                </label>
              </div>

              <button
                className="upload-button"
                onClick={handleUploadPayment}
                disabled={!paymentProof || isUploading || uploadSuccess}
                style={{
                  backgroundColor: uploadSuccess ? '#10b981' : isUploading ? '#6b7280' : '#000000',
                  color: 'white',
                  padding: '12px 24px',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '16px',
                  fontWeight: '600',
                  cursor: (!paymentProof || isUploading || uploadSuccess) ? 'not-allowed' : 'pointer',
                  opacity: (!paymentProof || isUploading || uploadSuccess) ? 0.6 : 1,
                  transition: 'all 0.2s ease',
                  marginTop: '16px',
                  width: '100%'
                }}
              >
                {isUploading ? (
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
                    <div style={{
                      width: '16px',
                      height: '16px',
                      border: '2px solid #ffffff',
                      borderTop: '2px solid transparent',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }}></div>
                    UPLOADING...
                  </div>
                ) : uploadSuccess ? (
                  'UPLOADED SUCCESSFULLY ✓'
                ) : (
                  'UPLOAD PAYMENT PROOF'
                )}
              </button>
            </div>

            {uploadSuccess && (
              <div style={{
                backgroundColor: '#d1fae5',
                border: '1px solid #10b981',
                borderRadius: '8px',
                padding: '12px',
                marginTop: '16px',
                color: '#065f46',
                textAlign: 'center' as const
              }}>
                Payment proof uploaded successfully! Proceeding to complete your booking...
              </div>
            )}
          </div>
        </div>
      </div>

      <style>{`
        .payment-proof-section {
          max-width: 600px;
          margin: 0 auto;
          padding: 24px;
        }

        .booking-summary {
          background: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          padding: 16px;
          margin: 24px 0;
        }

        .summary-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
        }

        .summary-item.total {
          font-weight: 600;
          border-top: 1px solid #e5e7eb;
          padding-top: 8px;
          margin-top: 8px;
        }

        .payment-method-section {
          margin: 24px 0;
        }

        .payment-methods {
          display: flex;
          gap: 16px;
          margin-top: 12px;
        }

        .payment-method {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 12px 16px;
          border: 2px solid #e5e7eb;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .payment-method.selected {
          border-color: #000000ff;
          background-color: #f3f4f6;
        }

        .payment-method input {
          margin: 0;
        }

        .file-upload-section {
          margin: 24px 0;
        }

        .file-upload-wrapper {
          margin: 12px 0;
        }

        .file-upload-label {
          display: block;
          padding: 12px 16px;
          border: 2px dashed #d1d5db;
          border-radius: 8px;
          text-align: center;
          cursor: pointer;
          transition: all 0.2s ease;
          background-color: #f9fafb;
        }

        .file-upload-label:hover {
          border-color: #000000ff;
          background-color: #f3f4f6;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
