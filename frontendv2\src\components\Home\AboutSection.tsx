import { useBranding } from '../../contexts/BrandingContext';

export default function AboutSection() {
  const { branding: brandingData } = useBranding();

  const aboutContent = {
    title: brandingData?.home?.aboutTitle || 'About Me',
    text: brandingData?.home?.aboutText || `Thank you for choosing Dammyspicy Beauty.

My name is <PERSON><PERSON>, and I am a licensed cosmetologist specializing in hair care and beauty treatments for natural hair, including microlocs and more. Based in Indianapolis, IN, my passion is helping women embrace their natural beauty with confidence.

My main objective is to bring out the beauty in each individual, put smiles on faces, and create styles that reflect uniqueness and elegance.

I'm excited to begin this healthy hair journey with you!`
  };

  return (
    <div className="info-card about-me-card">
      <h3 className="info-title">{aboutContent.title}</h3>
      <div className="info-content">
        {aboutContent.text.includes('<p>') ? (
          <div dangerouslySetInnerHTML={{ __html: aboutContent.text }} />
        ) : (
          aboutContent.text.split('\n\n').map((paragraph: string, index: number) => (
            <p key={index}>{paragraph}</p>
          ))
        )}
      </div>
    </div>
  )
}
