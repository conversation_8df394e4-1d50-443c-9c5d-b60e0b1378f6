{"name": "v2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:production": "tsc -b && vite build --config vite.config.production.ts", "build:analyze": "npm run build && npx vite-bundle-analyzer dist", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "preview:production": "vite preview --mode production", "type-check": "tsc --noEmit", "clean": "rm -rf dist build node_modules/.cache", "test:build": "npm run build && npm run preview"}, "dependencies": {"@types/react-datepicker": "^6.2.0", "@types/react-router-dom": "^5.3.3", "date-fns": "^4.1.0", "react": "^19.1.1", "react-calendar": "^6.0.0", "react-datepicker": "^8.5.0", "react-dom": "^19.1.1", "react-router-dom": "^7.8.0", "yet-another-react-lightbox": "^3.25.0"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "terser": "^5.44.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}