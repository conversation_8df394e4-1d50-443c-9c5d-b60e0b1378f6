import { useState, useEffect } from 'react'
import { Routes, Route, useNavigate } from 'react-router-dom'
import './App.css'
import './styles/admin.css'
import ScrollToTop from './components/ScrollToTop'
import { ToastProvider } from './contexts/ToastContext'
import { BrandingProvider } from './contexts/BrandingContext'
import HomePage from './pages/HomePage'
import AppointmentsPage from './pages/AppointmentsPage'
import ServicesPage from './pages/ServicesPage'
import SettingsPage from './pages/SettingsPage'
import DateTimeStep from './components/BookingFlow/DateTimeStep'
import DetailsStep from './components/BookingFlow/DetailsStep'
import PaymentProofStepWrapper from './components/BookingFlow/PaymentProofStepWrapper'
import CheckoutStep from './components/BookingFlow/CheckoutStep'
import AppointmentSuccess from './components/AppointmentSuccess'
import Login from './components/Auth/Login'
import UserDashboard from './components/Dashboard/UserDashboard'
import AdminDashboard from './components/Dashboard/AdminDashboard'
import ReviewsPage from './pages/ReviewsPage'
import {
  getCurrentUser,
  isAuthenticated,
  authAPI,
  type User
} from './utils/api'

function App() {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const navigate = useNavigate();

  // Check authentication on mount
  useEffect(() => {
    const user = getCurrentUser();
    if (user && isAuthenticated()) {
      setCurrentUser(user);
    }
  }, []);

  // Authentication handlers
  const handleLogin = (user: User) => {
    setCurrentUser(user);
    // Redirect based on user role
    if (user.role === 'admin') {
      navigate('/admin');
    } else {
      navigate('/dashboard');
    }
  };

  const handleLogout = async () => {
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('Logout error:', error);
    }

    setCurrentUser(null);
    navigate('/');
  };

  return (
    <BrandingProvider>
      <ToastProvider>
        <ScrollToTop />
        <Routes>
          <Route path="/" element={<HomePage currentUser={currentUser} onLogout={handleLogout} />} />
          <Route path="/login" element={<Login onLogin={handleLogin} />} />
          <Route path="/admin" element={
            !currentUser
              ? <Login onLogin={handleLogin} onSwitchToSignup={() => navigate('/login')} />
              : currentUser.role !== 'admin'
              ? <UserDashboard currentUser={currentUser} onLogout={handleLogout} onBookNew={() => navigate('/')} />
              : <AdminDashboard currentUser={currentUser} onLogout={handleLogout} />
          } />
          <Route path="/dashboard" element={
            !currentUser
              ? <Login onLogin={handleLogin} onSwitchToSignup={() => navigate('/login')} />
              : currentUser.role === 'admin'
              ? <AdminDashboard currentUser={currentUser} onLogout={handleLogout} />
              : <UserDashboard currentUser={currentUser} onLogout={handleLogout} onBookNew={() => navigate('/')} />
          } />
          <Route path="/appointments" element={<AppointmentsPage currentUser={currentUser} onLogout={handleLogout} />} />
          <Route path="/services" element={<ServicesPage currentUser={currentUser} onLogout={handleLogout} />} />
          <Route path="/settings" element={<SettingsPage currentUser={currentUser} onLogout={handleLogout} />} />
          <Route path="/reviews" element={<ReviewsPage />} />
          <Route path="/booking/datetime" element={<DateTimeStep />} />
          <Route path="/booking/details" element={<DetailsStep />} />
          <Route path="/booking/payment-proof" element={<PaymentProofStepWrapper />} />
          <Route path="/booking/checkout" element={<CheckoutStep />} />
          <Route path="/appointment-success" element={<AppointmentSuccess />} />
        </Routes>
      </ToastProvider>
    </BrandingProvider>
  );
}

export default App;
