import { useState, useEffect } from 'react';
import { useBranding } from '../../contexts/BrandingContext';
import { API_CONFIG } from '../../utils/config';

interface BrandingManagementProps {
  onError: (message: string) => void;
  onSuccess: (message: string) => void;
}

export default function BrandingManagement({ onError, onSuccess }: BrandingManagementProps) {
  const { branding, refreshBranding } = useBranding();
  const [activeSection, setActiveSection] = useState<'global' | 'home' | 'policies' | 'disclaimer' | 'microlocks' | 'consultation' | 'sectionVisibility'>('global');
  const [formData, setFormData] = useState<any>({});
  const [saving, setSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  useEffect(() => {
    if (branding) {
      setFormData(branding);
    }
  }, [branding]);

  const handleInputChange = (section: string, field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
    setHasUnsavedChanges(true);
  };

  const handleNestedChange = (section: string, nestedPath: string, value: any) => {
    const keys = nestedPath.split('.');
    setFormData((prev: any) => {
      const newData = { ...prev };
      let current = newData;
      
      // Navigate to the parent object
      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[section]) current[section] = {};
        if (!current[section][keys[i]]) current[section][keys[i]] = {};
        current = current[section];
        section = keys[i];
      }
      
      // Set the final value
      if (!current[section]) current[section] = {};
      current[section][keys[keys.length - 1]] = value;
      
      return newData;
    });
    setHasUnsavedChanges(true);
  };

  const handleArrayChange = (section: string, field: string, index: number, value: any) => {
    setFormData((prev: any) => {
      const newData = { ...prev };
      if (!newData[section]) newData[section] = {};
      if (!newData[section][field]) newData[section][field] = [];
      newData[section][field][index] = value;
      return newData;
    });
    setHasUnsavedChanges(true);
  };

  const addArrayItem = (section: string, field: string, defaultItem: any) => {
    setFormData((prev: any) => {
      const newData = { ...prev };
      if (!newData[section]) newData[section] = {};
      if (!newData[section][field]) newData[section][field] = [];
      newData[section][field].push(defaultItem);
      return newData;
    });
    setHasUnsavedChanges(true);
  };

  const removeArrayItem = (section: string, field: string, index: number) => {
    setFormData((prev: any) => {
      const newData = { ...prev };
      if (newData[section] && newData[section][field]) {
        newData[section][field].splice(index, 1);
      }
      return newData;
    });
    setHasUnsavedChanges(true);
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      
      const token = localStorage.getItem('authToken');
      const response = await fetch(`${API_CONFIG.BASE_URL}/branding`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        onSuccess('Branding updated successfully!');
        setHasUnsavedChanges(false);
        refreshBranding();
        // Signal to other tabs that branding was updated
        localStorage.setItem('branding_updated', 'true');
      } else {
        onError(data.message || 'Failed to update branding');
      }
    } catch (error: any) {
      console.error('Error saving branding:', error);
      onError('Failed to save branding changes');
    } finally {
      setSaving(false);
    }
  };

  const renderInputField = (label: string, value: string, onChange: (value: string) => void, type: string = 'text', rows?: number) => (
    <div className="form-group">
      <label className="form-label">{label}</label>
      {type === 'textarea' ? (
        <textarea
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          rows={rows || 3}
          className="form-input"
        />
      ) : (
        <input
          type={type}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          className="form-input"
        />
      )}
    </div>
  );

  const sections = [
    { id: 'global', label: '🌐 Global', icon: '🌐' },
    { id: 'home', label: '🏠 Home Page', icon: '🏠' },
    { id: 'sectionVisibility', label: '👁️ Section Visibility', icon: '👁️' },
    { id: 'policies', label: '📋 Policies', icon: '📋' },
    { id: 'disclaimer', label: '⚠️ Disclaimer', icon: '⚠️' },
    { id: 'microlocks', label: '💇‍♀️ Microlocks', icon: '💇‍♀️' },
    { id: 'consultation', label: '💬 Consultation', icon: '💬' }
  ];

  return (
    <div className="branding-management">
      <div className="section-header">
        <h2>Branding Management</h2>
        <p>Customize your website content and branding</p>
        {hasUnsavedChanges && (
          <div className="unsaved-changes-notice">
            ⚠️ You have unsaved changes
          </div>
        )}
      </div>

      <div className="branding-container">
        {/* Section Navigation */}
        <div className="section-nav">
          {sections.map((section) => (
            <button
              key={section.id}
              className={`section-nav-item ${activeSection === section.id ? 'active' : ''}`}
              onClick={() => setActiveSection(section.id as any)}
            >
              <span className="section-icon">{section.icon}</span>
              <span className="section-label">{section.label}</span>
            </button>
          ))}
        </div>

        {/* Content Area */}
        <div className="section-content">
          {activeSection === 'global' && (
            <div className="content-section">
              <h3>Global Settings</h3>
              {renderInputField(
                'Site Name',
                formData?.global?.siteName,
                (value) => handleInputChange('global', 'siteName', value)
              )}
              {renderInputField(
                'Tagline',
                formData?.global?.tagline,
                (value) => handleInputChange('global', 'tagline', value)
              )}
              {renderInputField(
                'Phone',
                formData?.global?.phone,
                (value) => handleInputChange('global', 'phone', value)
              )}
              {renderInputField(
                'Email',
                formData?.global?.email,
                (value) => handleInputChange('global', 'email', value),
                'email'
              )}
              {renderInputField(
                'Address',
                formData?.global?.address,
                (value) => handleInputChange('global', 'address', value)
              )}
            </div>
          )}

          {activeSection === 'home' && (
            <div className="content-section">
              <h3>Home Page Content</h3>
              {renderInputField(
                'Hero Title',
                formData?.home?.heroTitle,
                (value) => handleInputChange('home', 'heroTitle', value)
              )}
              {renderInputField(
                'Business Name',
                formData?.home?.businessName,
                (value) => handleInputChange('home', 'businessName', value)
              )}
              {renderInputField(
                'Location',
                formData?.home?.location,
                (value) => handleInputChange('home', 'location', value)
              )}
              {renderInputField(
                'Welcome Text',
                formData?.home?.welcomeText,
                (value) => handleInputChange('home', 'welcomeText', value)
              )}
              {renderInputField(
                'About Title',
                formData?.home?.aboutTitle,
                (value) => handleInputChange('home', 'aboutTitle', value)
              )}
              {renderInputField(
                'About Text',
                formData?.home?.aboutText,
                (value) => handleInputChange('home', 'aboutText', value),
                'textarea',
                6
              )}
              {renderInputField(
                'Testimonial Heading',
                formData?.home?.testimonialHeading,
                (value) => handleInputChange('home', 'testimonialHeading', value)
              )}
            </div>
          )}

          {activeSection === 'sectionVisibility' && (
            <div className="content-section">
              <h3>Section Visibility Controls</h3>
              <p>Toggle sections on/off for the landing page</p>

              <div className="toggle-controls">
                {[
                  { key: 'about', label: 'About Me Section' },
                  { key: 'policies', label: 'Policies Section' },
                  { key: 'disclaimer', label: 'Disclaimer Section' },
                  { key: 'microlocks', label: 'Microlocks Section' },
                  { key: 'consultation', label: 'Consultation Section' },
                  { key: 'priceList', label: 'Price List Section' }
                ].map((section) => (
                  <div key={section.key} className="toggle-item">
                    <label className="toggle-label">
                      <input
                        type="checkbox"
                        checked={formData?.home?.sectionVisibility?.[section.key] !== false}
                        onChange={(e) => {
                          const newVisibility = {
                            ...formData?.home?.sectionVisibility,
                            [section.key]: e.target.checked
                          };
                          handleInputChange('home', 'sectionVisibility', newVisibility);
                        }}
                      />
                      <span className="toggle-text">{section.label}</span>
                    </label>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeSection === 'policies' && (
            <div className="content-section">
              <h3>Policies & Terms</h3>
              {renderInputField(
                'Policies Title',
                formData?.home?.policies?.title,
                (value) => handleNestedChange('home', 'policies.title', value)
              )}
              {renderInputField(
                'Footer Text',
                formData?.home?.policies?.footerText,
                (value) => handleNestedChange('home', 'policies.footerText', value),
                'textarea',
                3
              )}

              <div className="subsection">
                <h4>Payment Options</h4>
                {renderInputField(
                  'CashApp Number',
                  formData?.home?.policies?.paymentOptions?.cashApp?.number,
                  (value) => handleNestedChange('home', 'policies.paymentOptions.cashApp.number', value)
                )}
                {renderInputField(
                  'Zelle Name',
                  formData?.home?.policies?.paymentOptions?.zelle?.name,
                  (value) => handleNestedChange('home', 'policies.paymentOptions.zelle.name', value)
                )}
                {renderInputField(
                  'Zelle Number',
                  formData?.home?.policies?.paymentOptions?.zelle?.number,
                  (value) => handleNestedChange('home', 'policies.paymentOptions.zelle.number', value)
                )}
              </div>

              <div className="subsection">
                <h4>Policy Sections</h4>
                {formData?.home?.policies?.sections?.map((section: any, index: number) => (
                  <div key={index} className="policy-section-item">
                    <div className="policy-section-header">
                      <h5>Policy {index + 1}</h5>
                      <button
                        type="button"
                        className="remove-button"
                        onClick={() => removeArrayItem('home', 'policies.sections', index)}
                      >
                        ❌
                      </button>
                    </div>
                    {renderInputField(
                      'Icon',
                      section.icon,
                      (value) => handleArrayChange('home', 'policies.sections', index, { ...section, icon: value })
                    )}
                    {renderInputField(
                      'Title',
                      section.title,
                      (value) => handleArrayChange('home', 'policies.sections', index, { ...section, title: value })
                    )}
                    {renderInputField(
                      'Content',
                      section.content,
                      (value) => handleArrayChange('home', 'policies.sections', index, { ...section, content: value }),
                      'textarea',
                      3
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  className="add-button"
                  onClick={() => addArrayItem('home', 'policies.sections', { icon: '📌', title: '', content: '' })}
                >
                  ➕ Add Policy Section
                </button>
              </div>
            </div>
          )}

          {activeSection === 'disclaimer' && (
            <div className="content-section">
              <h3>Disclaimer</h3>
              {renderInputField(
                'Disclaimer Title',
                formData?.home?.disclaimer?.title,
                (value) => handleNestedChange('home', 'disclaimer.title', value)
              )}
              {renderInputField(
                'Disclaimer Content',
                formData?.home?.disclaimer?.content,
                (value) => handleNestedChange('home', 'disclaimer.content', value),
                'textarea',
                8
              )}
            </div>
          )}

          {activeSection === 'microlocks' && (
            <div className="content-section">
              <h3>Microlocks Information</h3>
              {renderInputField(
                'Microlocks Title',
                formData?.home?.microlocks?.title,
                (value) => handleNestedChange('home', 'microlocks.title', value)
              )}
              {renderInputField(
                'Content',
                formData?.home?.microlocks?.content,
                (value) => handleNestedChange('home', 'microlocks.content', value),
                'textarea',
                4
              )}
              {renderInputField(
                'Methods Title',
                formData?.home?.microlocks?.methodsTitle,
                (value) => handleNestedChange('home', 'microlocks.methodsTitle', value)
              )}
              {renderInputField(
                'Duration Text',
                formData?.home?.microlocks?.durationText,
                (value) => handleNestedChange('home', 'microlocks.durationText', value),
                'textarea',
                2
              )}

              <div className="subsection">
                <h4>Methods</h4>
                {formData?.home?.microlocks?.methods?.map((method: string, index: number) => (
                  <div key={index} className="method-item">
                    {renderInputField(
                      `Method ${index + 1}`,
                      method,
                      (value) => {
                        const newMethods = [...(formData?.home?.microlocks?.methods || [])];
                        newMethods[index] = value;
                        handleNestedChange('home', 'microlocks.methods', newMethods);
                      }
                    )}
                    <button
                      type="button"
                      className="remove-button"
                      onClick={() => {
                        const newMethods = [...(formData?.home?.microlocks?.methods || [])];
                        newMethods.splice(index, 1);
                        handleNestedChange('home', 'microlocks.methods', newMethods);
                      }}
                    >
                      ❌
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  className="add-button"
                  onClick={() => {
                    const newMethods = [...(formData?.home?.microlocks?.methods || []), ''];
                    handleNestedChange('home', 'microlocks.methods', newMethods);
                  }}
                >
                  ➕ Add Method
                </button>
              </div>
            </div>
          )}

          {activeSection === 'consultation' && (
            <div className="content-section">
              <h3>Consultation Information</h3>
              {renderInputField(
                'Subtitle',
                formData?.home?.consultation?.subtitle,
                (value) => handleNestedChange('home', 'consultation.subtitle', value)
              )}
              {renderInputField(
                'Intro Text',
                formData?.home?.consultation?.intro,
                (value) => handleNestedChange('home', 'consultation.intro', value),
                'textarea',
                3
              )}
              {renderInputField(
                'Options Title',
                formData?.home?.consultation?.optionsTitle,
                (value) => handleNestedChange('home', 'consultation.optionsTitle', value)
              )}
              {renderInputField(
                'Duration',
                formData?.home?.consultation?.duration,
                (value) => handleNestedChange('home', 'consultation.duration', value)
              )}
              {renderInputField(
                'Note',
                formData?.home?.consultation?.note,
                (value) => handleNestedChange('home', 'consultation.note', value),
                'textarea',
                3
              )}
              {renderInputField(
                'CTA Text',
                formData?.home?.consultation?.ctaText,
                (value) => handleNestedChange('home', 'consultation.ctaText', value)
              )}
              {renderInputField(
                'Read More Text',
                formData?.home?.consultation?.readMoreText,
                (value) => handleNestedChange('home', 'consultation.readMoreText', value)
              )}
              {renderInputField(
                'Read Less Text',
                formData?.home?.consultation?.readLessText,
                (value) => handleNestedChange('home', 'consultation.readLessText', value)
              )}
            </div>
          )}

          {/* Save Button */}
          <div className="save-section">
            <button
              className={`save-button ${saving ? 'saving' : ''} ${hasUnsavedChanges ? 'has-changes' : ''}`}
              onClick={handleSave}
              disabled={saving || !hasUnsavedChanges}
            >
              {saving ? '💾 Saving...' : hasUnsavedChanges ? '💾 Save Changes' : '✅ Saved'}
            </button>
          </div>
        </div>
      </div>

      <style>{`
        .branding-management {
          padding: 1rem;
        }

        .section-header {
          margin-bottom: 2rem;
        }

        .section-header h2 {
          margin: 0 0 0.5rem 0;
          color: #1f2937;
        }

        .section-header p {
          margin: 0;
          color: #6b7280;
        }

        .unsaved-changes-notice {
          background: #fef3c7;
          color: #92400e;
          padding: 0.5rem 1rem;
          border-radius: 0.5rem;
          margin-top: 1rem;
          font-size: 0.875rem;
        }

        .branding-container {
          display: grid;
          grid-template-columns: 250px 1fr;
          gap: 2rem;
          background: white;
          border-radius: 0.5rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }

        .section-nav {
          background: #f9fafb;
          padding: 1rem;
          border-right: 1px solid #e5e7eb;
        }

        .section-nav-item {
          display: flex;
          align-items: center;
          width: 100%;
          padding: 0.75rem;
          margin-bottom: 0.5rem;
          border: none;
          background: transparent;
          border-radius: 0.375rem;
          cursor: pointer;
          transition: all 0.2s;
          text-align: left;
        }

        .section-nav-item:hover {
          background: #e5e7eb;
        }

        .section-nav-item.active {
          background: #3b82f6;
          color: white;
        }

        .section-icon {
          margin-right: 0.5rem;
          font-size: 1.125rem;
        }

        .section-label {
          font-size: 0.875rem;
          font-weight: 500;
        }

        .section-content {
          padding: 2rem;
          max-height: 80vh;
          overflow-y: auto;
        }

        .content-section h3 {
          margin: 0 0 1.5rem 0;
          color: #1f2937;
          font-size: 1.25rem;
        }

        .form-group {
          margin-bottom: 1.5rem;
        }

        .form-label {
          display: block;
          margin-bottom: 0.5rem;
          font-weight: 500;
          color: #374151;
          font-size: 0.875rem;
        }

        .form-input {
          width: 100%;
          padding: 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          transition: border-color 0.2s;
        }

        .form-input:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .subsection {
          margin-top: 2rem;
          padding-top: 1.5rem;
          border-top: 1px solid #e5e7eb;
        }

        .subsection h4 {
          margin: 0 0 1rem 0;
          color: #1f2937;
          font-size: 1.125rem;
        }

        .policy-section-item,
        .method-item {
          background: #f9fafb;
          padding: 1rem;
          border-radius: 0.375rem;
          margin-bottom: 1rem;
          border: 1px solid #e5e7eb;
        }

        .policy-section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
        }

        .policy-section-header h5 {
          margin: 0;
          color: #1f2937;
        }

        .add-button,
        .remove-button {
          padding: 0.5rem 1rem;
          border: none;
          border-radius: 0.375rem;
          cursor: pointer;
          font-size: 0.875rem;
          transition: all 0.2s;
        }

        .add-button {
          background: #10b981;
          color: white;
        }

        .add-button:hover {
          background: #059669;
        }

        .remove-button {
          background: #ef4444;
          color: white;
          padding: 0.25rem 0.5rem;
          font-size: 0.75rem;
        }

        .remove-button:hover {
          background: #dc2626;
        }

        .save-section {
          margin-top: 2rem;
          padding-top: 1.5rem;
          border-top: 1px solid #e5e7eb;
          text-align: right;
        }

        .save-button {
          padding: 0.75rem 1.5rem;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          background: #6b7280;
          color: white;
        }

        .save-button.has-changes {
          background: #3b82f6;
        }

        .save-button.has-changes:hover {
          background: #2563eb;
        }

        .save-button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .save-button.saving {
          background: #f59e0b;
        }

        @media (max-width: 768px) {
          .branding-container {
            grid-template-columns: 1fr;
          }

          .section-nav {
            display: flex;
            overflow-x: auto;
            padding: 1rem 0.5rem;
          }

          .section-nav-item {
            flex-shrink: 0;
            margin-right: 0.5rem;
            margin-bottom: 0;
          }
        }
      `}</style>
    </div>
  );
}
