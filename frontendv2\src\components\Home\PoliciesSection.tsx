import { useBranding } from '../../contexts/BrandingContext';

export default function PoliciesSection() {
  const { branding: brandingData } = useBranding();

  const policies = brandingData?.home?.policies;

  return (
    <div className="policies-section">
      <div className="policies-card">
        {/* Company Header */}
        <div className="policies-header">
          <h1 className="company-name">
            {policies?.companyName || 'DammySpicy Beauty LLC'}
          </h1>
          <p className="company-tagline">
            {policies?.tagline || 'Where Beauty Meets Intention'}
          </p>
          <div className="header-divider"></div>
        </div>

        <h2 className="policies-title">
          {policies?.title || 'Policies & Terms of Service'}
        </h2>

        <div className="policies-content">
          <ul className="policies-list">
            {policies?.sections?.map((section: any, index: number) => (
              <li key={section._id || index} className="policy-item">
                <div className="policy-icon">{section.icon}</div>
                <div className="policy-content">
                  <strong className="policy-title">{section.title}</strong>
                  <div className="policy-text" dangerouslySetInnerHTML={{ __html: section.content }} />
                </div>
              </li>
            )) || (
              <>
                <li className="policy-item">
                  <div className="policy-icon">📌</div>
                  <div className="policy-content">
                    <strong className="policy-title">TO BOOK</strong>
                    <div className="policy-text">A non-refundable deposit is required to secure any service. This amount goes toward your total service.</div>
                  </div>
                </li>
                <li className="policy-item">
                  <div className="policy-icon">⏰</div>
                  <div className="policy-content">
                    <strong className="policy-title">REMAINING BALANCE</strong>
                    <div className="policy-text">CASH ONLY is accepted at time of your appointment. No digital payments will be accepted</div>
                  </div>
                </li>
                <li className="policy-item">
                  <div className="policy-icon">🚫</div>
                  <div className="policy-content">
                    <strong className="policy-title">RESCHEDULING</strong>
                    <div className="policy-text">Same-day reschedule - loss of deposit</div>
                  </div>
                </li>
                <li className="policy-item">
                  <div className="policy-icon">❌</div>
                  <div className="policy-content">
                    <strong className="policy-title">CANCELLATIONS</strong>
                    <div className="policy-text">All services made at least 24 hours before your appointment.</div>
                  </div>
                </li>
                <li className="policy-item">
                  <div className="policy-icon">🚫</div>
                  <div className="policy-content">
                    <strong className="policy-title">NO REFUNDS</strong>
                    <div className="policy-text">All services are final once completed.</div>
                  </div>
                </li>
              </>
            )}
          </ul>
        </div>

        <div className="policies-footer">
          <p>
            {policies?.footerText ||
             `Thank you for choosing ${brandingData?.global?.siteName || 'DammySpicy Beauty LLC'}. We appreciate your business and look forward to serving you with love and excellence!`
            }
          </p>
        </div>
      </div>
    </div>
  )
}
