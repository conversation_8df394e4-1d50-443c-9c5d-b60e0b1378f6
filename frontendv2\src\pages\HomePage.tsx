import { useState, useEffect } from 'react'
import { useNavigate, Link } from 'react-router-dom'
import Header from '../components/Layout/Header'
import HeroSection from '../components/Home/HeroSection'
import AboutSection from '../components/Home/AboutSection'
import MicrolocksSection from '../components/Home/MicrolocksSection'
import PriceListSection from '../components/Home/PriceListSection'
import ConsultationSection from '../components/Home/ConsultationSection'
import PoliciesSection from '../components/Home/PoliciesSection'
import DisclaimerSection from '../components/Home/DisclaimerSection'
import { type User } from '../utils/api'
import { API_CONFIG } from '../utils/config'
import { useBranding } from '../contexts/BrandingContext'


interface Review {
  _id: string;
  service?: {
    _id: string;
    name: string;
  };
  user?: {
    _id: string;
    firstName: string;
    lastName: string;
  };
  rating: number;
  title?: string;
  comment?: string;
  customerName?: string;
  status: 'pending' | 'approved' | 'rejected';
  isVerifiedPurchase: boolean;
  createdAt: string;
}



interface HomePageProps {
  currentUser: User | null;
  onLogout: () => void;
}

export default function HomePage({ currentUser, onLogout }: HomePageProps) {
  const navigate = useNavigate();
  const [showConsultationDetails, setShowConsultationDetails] = useState(false);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [reviewsLoading, setReviewsLoading] = useState(true);
  const { branding: brandingData } = useBranding();

  useEffect(() => {
    loadReviews();
  }, []);

  const loadReviews = async () => {
    try {
      setReviewsLoading(true);
      const response = await fetch(`${API_CONFIG.BASE_URL}/reviews?limit=3&status=approved`);
      const data = await response.json();

      if (data.success) {
        setReviews(data.data.reviews || []);
      }
    } catch (error) {
      console.error('Error loading reviews:', error);
      setReviews([]);
    } finally {
      setReviewsLoading(false);
    }
  };

  const handleBookService = (service: any) => {
    // Navigate to booking flow with service data
    const searchParams = new URLSearchParams({
      service: JSON.stringify(service)
    });
    navigate(`/booking/datetime?${searchParams.toString()}`);
  };

  // Navigation handlers are handled by the Header component now

  return (
    <div className="app">
      <Header
        currentUser={currentUser}
        onLogout={onLogout}
      />

      <main className="main-content">
        <HeroSection />
        
        <div className="info-sections">
          <AboutSection />
          <PoliciesSection />
          <DisclaimerSection />



          <MicrolocksSection />
          <PriceListSection onBookService={handleBookService} />

          {/* Reviews Section */}
          <div className="reviews-section">
            <div className="section-container">
              <div className="section-header">
                <h2 className="section-title">
                  {brandingData?.home?.testimonialHeading || 'Client Reviews'}
                </h2>
                <p className="section-subtitle">
                  See what our clients are saying about their experience
                </p>
              </div>

              <div className="reviews-content">
                {reviewsLoading ? (
                  <div className="loading-container">
                    <p>Loading reviews...</p>
                  </div>
                ) : reviews.length === 0 ? (
                  <div className="empty-state">
                    <h3>No reviews yet</h3>
                    <p>Be the first to share your experience!</p>
                  </div>
                ) : (
                  <div className="reviews-grid">
                    {reviews.map((review) => (
                      <div key={review._id} className="review-card">
                        <div className="review-stars">
                          {'★'.repeat(review.rating)}{'☆'.repeat(5 - review.rating)}
                        </div>
                        <p className="review-text">
                          {review.comment ? `"${review.comment}"` : `"${review.title || 'Great service!'}"`}
                        </p>
                        <p className="review-author">
                          - {review.user ? `${review.user.firstName} ${review.user.lastName.charAt(0)}.` : review.customerName || 'Anonymous'}
                        </p>
                      </div>
                    ))}
                  </div>
                )}

                <div className="reviews-actions">
                  <Link
                    to="/reviews?tab=create"
                    className="btn btn-primary"
                  >
                    Share Your Experience
                  </Link>
                  <Link
                    to="/reviews"
                    className="btn btn-outline"
                  >
                    View All Reviews
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        <ConsultationSection
          onBookService={handleBookService}
          showDetails={showConsultationDetails}
          onToggleDetails={() => setShowConsultationDetails(!showConsultationDetails)}
        />
      </main>
    </div>
  )
}
