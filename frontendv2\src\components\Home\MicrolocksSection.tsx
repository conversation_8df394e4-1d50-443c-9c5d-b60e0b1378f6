import { useBranding } from '../../contexts/BrandingContext';

export default function MicrolocksSection() {
  const { branding: brandingData } = useBranding();

  const microlocks = brandingData?.home?.microlocks;

  return (
    <div className="info-card microlocks-card">
      <h3 className="info-title">
        {microlocks?.title || 'Microlocks'}
      </h3>
      <div className="info-content">
        {microlocks?.content ? (
          <div dangerouslySetInnerHTML={{ __html: microlocks.content }} />
        ) : (
          <p>Full payment is required to secure all Microlock establishment appointments. This must be paid at the time of scheduling.</p>
        )}

        <p className="methods-title">
          {microlocks?.methodsTitle || '3 STARTING METHODS TO CHOOSE FROM:'}
        </p>

        <ul className="methods-list">
          {microlocks?.methods?.map((method: string, index: number) => (
            <li key={index}>{method}</li>
          )) || (
            <>
              <li>1 - INTERLOCKS</li>
              <li>2 - TWO STRAND TWIST</li>
              <li>3 - BRAID LOCS</li>
            </>
          )}
        </ul>

        {microlocks?.durationText && (
          <p>{microlocks.durationText}</p>
        )}
      </div>
    </div>
  )
}
