import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { formatTo12Hour } from '../utils/timeFormat';

interface AppointmentData {
  id: string;
  userId?: string;
  serviceId: string;
  serviceName: string;
  servicePrice: number;
  date: string;
  time?: string;
  status: string;
  customerInfo?: {
    name: string;
    email: string;
    phone: string;
  };
  totalPrice: number;
  addOns?: Array<{
    id: string;
    name: string;
    price: number;
    duration: number;
  }>;
  notes?: string;
  paymentProofs?: Array<{
    id: string;
    amount: number;
    paymentMethod: 'cashapp' | 'zelle';
    proofImage: string;
    status: 'pending' | 'verified' | 'rejected';
    notes?: string;
    createdAt: string;
    _id?: string;
  }>;
  paymentStatus?: 'pending' | 'completed' | 'failed';
  createdAt: string;
  error?: boolean;
  errorMessage?: string;
  paymentProofUploaded?: boolean;
  paymentProofUrl?: string;
}

export default function AppointmentSuccess() {
  const location = useLocation();
  const navigate = useNavigate();
  const [appointmentData, setAppointmentData] = useState<AppointmentData | null>(null);

  useEffect(() => {
    const loadAppointmentData = () => {
      // Get appointment data from localStorage first, then fallback to location state
      const savedData = localStorage.getItem('appointmentSuccessData');
      console.log('AppointmentSuccess: Checking localStorage for data:', savedData);

      if (savedData) {
        try {
          const parsedData = JSON.parse(savedData);
          console.log('AppointmentSuccess: Successfully parsed data from localStorage:', parsedData);
          setAppointmentData(parsedData);
        } catch (error) {
          console.error('Error parsing appointment data from localStorage:', error);
          // Fallback to location state
          setAppointmentData(location.state?.appointmentData as AppointmentData);
        }
      } else {
        console.log('AppointmentSuccess: No data in localStorage, checking location state');
        // Fallback to location state
        setAppointmentData(location.state?.appointmentData as AppointmentData);
      }
    };

    // Load data initially
    loadAppointmentData();

    // Listen for storage changes (for testing purposes)
    const handleStorageChange = () => {
      console.log('AppointmentSuccess: Storage changed, reloading data');
      loadAppointmentData();
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [location.state]);

  // Clear localStorage when user navigates away using browser back/forward
  useEffect(() => {
    const handlePopState = () => {
      // Only clear if user is actually navigating away from success page
      if (!window.location.pathname.includes('/appointment-success')) {
        console.log('AppointmentSuccess: Clearing localStorage on navigation away from success page');
        localStorage.removeItem('appointmentSuccessData');
      }
    };

    window.addEventListener('popstate', handlePopState);

    // Cleanup function to remove event listeners only
    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, []);

  // Handle error state
  if (appointmentData?.error) {
    return (
      <div className="app">
        <header className="header">
          <div className="header-content">
            <div className="header-left">
              <h1>dammyspicybeauty</h1>
            </div>
            <div className="auth-links">
              <button onClick={() => {
                localStorage.removeItem('appointmentSuccessData');
                navigate('/');
              }} className="auth-link">HOME</button>
            </div>
          </div>
        </header>

        <main className="main-content" style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', minHeight: '60vh' }}>
          <div className="appointment-card" style={{ textAlign: 'center', maxWidth: '500px' }}>
            <div style={{
              width: '80px',
              height: '80px',
              backgroundColor: '#f8d7da',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 1rem',
              border: '3px solid #dc3545'
            }}>
              <svg style={{ width: '40px', height: '40px', color: '#dc3545' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#dc3545', marginBottom: '1rem' }}>
              Appointment Error
            </h2>
            <p style={{ color: '#666', marginBottom: '1rem' }}>
              {appointmentData.errorMessage || 'There was an issue with your appointment.'}
            </p>
            {appointmentData.paymentProofUploaded && (
              <p style={{ color: '#28a745', marginBottom: '2rem' }}>
                ✓ Your payment proof was uploaded successfully. Please contact support with this information.
              </p>
            )}
            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
              <button
                onClick={() => {
                  localStorage.removeItem('appointmentSuccessData');
                  navigate('/');
                }}
                className="continue-button"
                style={{
                  backgroundColor: '#2c5530',
                  color: 'white',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '8px',
                  border: 'none',
                  fontSize: '1rem',
                  fontWeight: '600',
                  cursor: 'pointer'
                }}
              >
                Go Home
              </button>
              <button
                onClick={() => {
                  localStorage.removeItem('appointmentSuccessData');
                  navigate('/contact');
                }}
                style={{
                  backgroundColor: '#f8f9fa',
                  color: '#333',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '8px',
                  border: '1px solid #dee2e6',
                  fontSize: '1rem',
                  fontWeight: '600',
                  cursor: 'pointer'
                }}
              >
                Contact Support
              </button>
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (!appointmentData) {
    return (
      <div className="app">
        <header className="header">
          <div className="header-content">
            <div className="header-left">
              <h1>dammyspicybeauty</h1>
            </div>
            <div className="auth-links">
              <button onClick={() => {
                localStorage.removeItem('appointmentSuccessData');
                navigate('/');
              }} className="auth-link">HOME</button>
            </div>
          </div>
        </header>

        <main className="main-content" style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', minHeight: '60vh' }}>
          <div className="appointment-card" style={{ textAlign: 'center', maxWidth: '400px' }}>
            <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#333', marginBottom: '1rem' }}>
              No Appointment Data
            </h2>
            <p style={{ color: '#666', marginBottom: '2rem' }}>
              We couldn't find your appointment information.
            </p>
            <button
              onClick={() => navigate('/')}
              className="continue-button"
              style={{
                backgroundColor: '#2c5530',
                color: 'white',
                padding: '0.75rem 1.5rem',
                borderRadius: '8px',
                border: 'none',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer'
              }}
            >
              Go Home
            </button>
          </div>
        </main>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    try {
      // Handle ISO date format from API
      const date = new Date(dateString);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }

      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.warn('Error formatting date:', error);
      return 'Invalid Date';
    }
  };

  // Use centralized time formatting utility
  const formatTime = formatTo12Hour;

  return (
    <div className="app">
      <header className="header">
        <div className="header-content">
          <div className="header-left">
            <h1>dammyspicybeauty</h1>
          </div>
          <div className="auth-links">
            <button onClick={() => navigate('/')} className="auth-link">HOME</button>
          </div>
        </div>
      </header>

      <main className="main-content">
        {/* Success Header */}
        <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
          <div style={{
            width: '80px',
            height: '80px',
            backgroundColor: '#d4edda',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 1rem',
            border: '3px solid #28a745'
          }}>
            <svg style={{ width: '40px', height: '40px', color: '#28a745' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h1 style={{ fontSize: '2.5rem', fontWeight: 'bold', color: '#2c5530', marginBottom: '0.5rem' }}>
            Appointment Confirmed!
          </h1>
          <p style={{ color: '#666', fontSize: '1.1rem' }}>Your appointment has been successfully booked.</p>
        </div>

        {/* Appointment Details Card */}
        <div className="appointment-card" style={{ marginBottom: '2rem' }}>
          <div className="appointment-header">
            <h2 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#2c5530', marginBottom: '1rem' }}>
              Appointment Details
            </h2>
          </div>

          <div className="appointment-details">
            {/* Service */}
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '1rem' }}>
              <div>
                <h4 style={{ fontSize: '1.1rem', fontWeight: '600', color: '#333', marginBottom: '0.25rem' }}>
                  {appointmentData.serviceName}
                </h4>
                <p style={{ fontSize: '0.9rem', color: '#666' }}>Service</p>
              </div>
              <p className="appointment-price" style={{ fontSize: '1.1rem', fontWeight: '600', color: '#2c5530' }}>
                ${appointmentData.servicePrice}
              </p>
            </div>

            {/* Date & Time */}
            <div style={{ borderTop: '1px solid #eee', paddingTop: '1rem', marginBottom: '1rem' }}>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '1rem' }}>
                <div>
                  <p className="appointment-datetime" style={{ fontSize: '1rem', fontWeight: '600', color: '#333', marginBottom: '0.25rem' }}>
                    {formatDate(appointmentData.date)}
                  </p>
                  <p style={{ fontSize: '0.9rem', color: '#666' }}>Date</p>
                </div>
                <div>
                  <p className="appointment-datetime" style={{ fontSize: '1rem', fontWeight: '600', color: '#333', marginBottom: '0.25rem' }}>
                    {formatTime(appointmentData.time || '')}
                  </p>
                  <p style={{ fontSize: '0.9rem', color: '#666' }}>Time</p>
                </div>
                <div>
                  <p style={{
                    fontSize: '1rem',
                    fontWeight: '600',
                    color: appointmentData.status === 'confirmed' ? '#28a745' :
                           appointmentData.status === 'pending' ? '#ffc107' : '#6c757d',
                    marginBottom: '0.25rem'
                  }}>
                    {(appointmentData.status || 'PENDING').toUpperCase()}
                  </p>
                  <p style={{ fontSize: '0.9rem', color: '#666' }}>Status</p>
                </div>
                <div>
                  <p style={{
                    fontSize: '1rem',
                    fontWeight: '600',
                    color: appointmentData.paymentStatus === 'completed' ? '#28a745' :
                           appointmentData.paymentStatus === 'pending' ? '#ffc107' : '#6c757d',
                    marginBottom: '0.25rem'
                  }}>
                    {(appointmentData.paymentStatus || 'PENDING').toUpperCase()}
                  </p>
                  <p style={{ fontSize: '0.9rem', color: '#666' }}>Payment</p>
                </div>
              </div>
            </div>

            {/* Customer Info */}
            <div style={{ borderTop: '1px solid #eee', paddingTop: '1rem', marginBottom: '1rem' }}>
              <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#333', marginBottom: '0.5rem' }}>
                Customer Information
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
                <p style={{ color: '#555' }}>{appointmentData.customerInfo?.name || 'N/A'}</p>
                <p style={{ color: '#555' }}>{appointmentData.customerInfo?.email || 'N/A'}</p>
                <p style={{ color: '#555' }}>{appointmentData.customerInfo?.phone || 'N/A'}</p>
              </div>
            </div>

            {/* Add-ons */}
            {appointmentData.addOns && appointmentData.addOns.length > 0 && (
              <div style={{ borderTop: '1px solid #eee', paddingTop: '1rem', marginBottom: '1rem' }}>
                <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#333', marginBottom: '0.5rem' }}>Add-ons</h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
                  {appointmentData.addOns.map((addOn: any, index: number) => (
                    <div key={index} style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span style={{ color: '#555' }}>{addOn.name}</span>
                      <span style={{ color: '#555' }}>${addOn.price}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Payment Proof */}
            {appointmentData.paymentProofs && appointmentData.paymentProofs.length > 0 && (
              <div style={{ borderTop: '1px solid #eee', paddingTop: '1rem', marginBottom: '1rem' }}>
                <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#333', marginBottom: '0.5rem' }}>Payment Proof</h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  {appointmentData.paymentProofs.map((proof, index) => (
                    <div key={proof.id || proof._id || index} style={{
                      padding: '1rem',
                      backgroundColor: '#f8f9fa',
                      borderRadius: '8px',
                      border: '1px solid #e9ecef'
                    }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.75rem' }}>
                        <span style={{ fontWeight: '600', color: '#333', fontSize: '1rem' }}>
                          {(proof.paymentMethod || 'UNKNOWN').toUpperCase()} - ${proof.amount}
                        </span>
                        <span style={{
                          padding: '0.375rem 0.75rem',
                          borderRadius: '6px',
                          fontSize: '0.8rem',
                          fontWeight: '600',
                          backgroundColor: proof.status === 'verified' ? '#d4edda' :
                                         proof.status === 'rejected' ? '#f8d7da' : '#fff3cd',
                          color: proof.status === 'verified' ? '#155724' :
                                 proof.status === 'rejected' ? '#721c24' : '#856404'
                        }}>
                          {(proof.status || 'PENDING').toUpperCase()}
                        </span>
                      </div>

                      {/* Payment Proof Image */}
                      {proof.proofImage && (
                        <div style={{ marginBottom: '0.75rem' }}>
                          <p style={{ fontSize: '0.9rem', color: '#666', marginBottom: '0.5rem' }}>Payment Screenshot:</p>
                          <div style={{
                            border: '2px solid #dee2e6',
                            borderRadius: '8px',
                            overflow: 'hidden',
                            maxWidth: '300px',
                            cursor: 'pointer'
                          }}
                          onClick={() => window.open(proof.proofImage, '_blank')}
                          >
                            <img
                              src={proof.proofImage}
                              alt="Payment proof"
                              style={{
                                width: '100%',
                                height: 'auto',
                                display: 'block',
                                transition: 'transform 0.2s ease'
                              }}
                              onMouseOver={(e) => (e.target as HTMLImageElement).style.transform = 'scale(1.02)'}
                              onMouseOut={(e) => (e.target as HTMLImageElement).style.transform = 'scale(1)'}
                            />
                          </div>
                          <p style={{ fontSize: '0.75rem', color: '#6c757d', marginTop: '0.25rem', textAlign: 'center' }}>
                            Click to view full size
                          </p>
                        </div>
                      )}

                      <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
                        <p style={{ fontSize: '0.85rem', color: '#666', margin: 0 }}>
                          <strong>Submitted:</strong> {new Date(proof.createdAt).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                        {proof.notes && (
                          <p style={{ fontSize: '0.85rem', color: '#666', margin: 0 }}>
                            <strong>Notes:</strong> {proof.notes}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Notes */}
            {appointmentData.notes && (
              <div className="appointment-notes" style={{ borderTop: '1px solid #eee', paddingTop: '1rem', marginBottom: '1rem' }}>
                <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#333', marginBottom: '0.5rem' }}>Notes</h3>
                <p style={{ color: '#555' }}>{appointmentData.notes}</p>
              </div>
            )}

            {/* Total */}
            <div style={{ borderTop: '2px solid #2c5530', paddingTop: '1rem' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <p style={{ fontSize: '1.25rem', fontWeight: '600', color: '#333' }}>Total</p>
                <p style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#2c5530' }}>${appointmentData.totalPrice}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Confirmation ID and Details */}
        <div style={{
          backgroundColor: '#e3f2fd',
          borderRadius: '8px',
          padding: '1.25rem',
          marginBottom: '2rem',
          border: '1px solid #bbdefb'
        }}>
          <div style={{ marginBottom: '0.75rem' }}>
            <p style={{ fontSize: '0.95rem', color: '#1565c0', marginBottom: '0.25rem' }}>
              <span style={{ fontWeight: '600' }}>Confirmation ID:</span> {appointmentData.id}
            </p>
            <p style={{ fontSize: '0.85rem', color: '#1976d2', marginBottom: '0.5rem' }}>
              Appointment created: {new Date(appointmentData.createdAt).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </p>
          </div>
          <div style={{
            backgroundColor: 'rgba(255, 255, 255, 0.7)',
            borderRadius: '6px',
            padding: '0.75rem',
            border: '1px solid rgba(187, 222, 251, 0.5)'
          }}>
            <p style={{ fontSize: '0.85rem', color: '#1565c0', margin: 0, lineHeight: '1.4' }}>
              <strong>Important:</strong> Please save this confirmation ID for your records.
              {appointmentData.paymentStatus === 'pending'
                ? ' Your payment proof is being reviewed and you will receive confirmation once verified.'
                : ' You will receive a confirmation email shortly.'
              }
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div style={{
          display: 'flex',
          flexDirection: window.innerWidth > 768 ? 'row' : 'column',
          gap: '1rem',
          marginBottom: '2rem'
        }}>
          <button
            onClick={() => {
              localStorage.removeItem('appointmentSuccessData');
              navigate('/');
            }}
            className="continue-button"
            style={{
              backgroundColor: '#2c5530',
              color: 'white',
              padding: '1rem 2rem',
              borderRadius: '8px',
              border: 'none',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'background-color 0.3s'
            }}
            onMouseOver={(e) => (e.target as HTMLButtonElement).style.backgroundColor = '#1e3a21'}
            onMouseOut={(e) => (e.target as HTMLButtonElement).style.backgroundColor = '#2c5530'}
          >
            Book Another Appointment
          </button>
          <button
            onClick={() => window.print()}
            style={{
              backgroundColor: '#f8f9fa',
              color: '#333',
              padding: '1rem 2rem',
              borderRadius: '8px',
              border: '1px solid #dee2e6',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'background-color 0.3s'
            }}
            onMouseOver={(e) => (e.target as HTMLButtonElement).style.backgroundColor = '#e9ecef'}
            onMouseOut={(e) => (e.target as HTMLButtonElement).style.backgroundColor = '#f8f9fa'}
          >
            Print Details
          </button>
        </div>


      </main>
    </div>
  );
}
