import { useState, useEffect } from 'react';
import { adminAPI, type AdminAppointment, type Customer, type User } from '../../utils/api';
import '../../styles/admin.css';

// Extended admin appointment interface with totalPrice
interface ExtendedAdminAppointment extends AdminAppointment {
  totalPrice?: number;
}

// Extended customer interface with role
interface ExtendedCustomer extends Customer {
  role?: string;
}
import AppointmentManagement from '../Admin/AppointmentManagement';
import ServiceManagement from '../Admin/ServiceManagement';
import ReviewManagement from '../Admin/ReviewManagement';
import CalendarManagement from '../Admin/CalendarManagement';
import BrandingManagement from '../Admin/BrandingManagement';
// Analytics import removed - not currently used
import LoadingSpinner from '../LoadingSpinner';
import ServiceModal from '../ServiceModal';
// Real-time updates removed

interface AdminDashboardProps {
  currentUser: User;
  onLogout: () => void;
}

export default function AdminDashboard({ currentUser, onLogout }: AdminDashboardProps) {
  const [user] = useState(currentUser);
  const [users, setUsers] = useState<Customer[]>([]);
  const [appointments, setAppointments] = useState<AdminAppointment[]>([]);
  const [activeTab, setActiveTab] = useState<'dashboard' | 'appointments' | 'calendar' | 'customers' | 'services' | 'reviews' | 'branding'>('dashboard');
  const [dashboardData, setDashboardData] = useState<{
    services?: any[];
    appointments?: any[];
    customers?: any[];
    stats?: any;
    overview?: {
      totalAppointments?: number;
      todayAppointments?: number;
      totalUsers?: number;
      totalRevenue?: number;
    };
    recentAppointments?: any[];
    statistics?: {
      appointments?: Record<string, number>;
    };
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [showServiceModal, setShowServiceModal] = useState(false);
  const [editingService, setEditingService] = useState<any>(null);

  const [toastMessage, setToastMessage] = useState<string | null>(null);
  const [toastType, setToastType] = useState<'success' | 'error'>('success');
  // Remove duplicate login logic since authentication is handled at route level

  useEffect(() => {
    loadData();
  }, [activeTab]); // Reload data when tab changes

  // Load dashboard data on initial mount
  useEffect(() => {
    if (activeTab === 'dashboard') {
      loadData();
    }
  }, []); // Run once on mount

  // Real-time updates removed - users will refresh when needed

  const loadData = async () => {
    try {
      setLoading(true);

      console.log('🔄 Loading dashboard data...');
      console.log('🔑 Auth token:', localStorage.getItem('authToken'));
      console.log('👤 Current user:', localStorage.getItem('currentUser'));

      // Only load data for the current active tab to avoid unnecessary API calls
      if (activeTab === 'dashboard') {
        // Load dashboard overview data
        const response = await adminAPI.getDashboard();
        console.log('📊 Dashboard API response:', response);

        if (response.success) {
          console.log('✅ Dashboard data loaded successfully:', response.data);
          setDashboardData(response.data);
        } else {
          console.error('❌ Dashboard API failed:', response);
        }
      } else if (activeTab === 'customers') {
        // Load customers data only
        const response = await adminAPI.getCustomers();
        console.log('👥 Customers API response:', response);

        if (response.success) {
          console.log('✅ Customers data loaded successfully:', response.data);
          setUsers(response.data.customers || []);
        } else {
          console.error('❌ Customers API failed:', response);
        }
      } else if (activeTab === 'appointments') {
        // Load appointments data only
        const response = await adminAPI.getAppointments();
        console.log('📅 Appointments API response:', response);

        if (response.success) {
          console.log('✅ Appointments data loaded successfully:', response.data);
          setAppointments(response.data.appointments || []);
        } else {
          console.error('❌ Appointments API failed:', response);
        }
      } else if (activeTab === 'services') {
        // Load services data only
        const response = await adminAPI.getServices();
        console.log('🛠️ Services API response:', response);

        if (response.success) {
          console.log('✅ Services data loaded successfully:', response.data);
          setDashboardData(prev => ({
            ...prev,
            services: response.data.services || []
          }));
        } else {
          console.error('❌ Services API failed:', response);
        }
      } else if (activeTab === 'reviews') {
        // Reviews are handled by the ReviewManagement component
        console.log('📝 Reviews tab selected - handled by ReviewManagement component');
      }
    } catch (error) {
      console.error('💥 Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Service management functions - now handled by ServiceManagement component
  // const handleEditService = (service: any) => {
  //   setEditingService(service);
  //   setShowServiceModal(true);
  // };

  // const handleDeleteService = async (serviceId: string) => {
  //   if (window.confirm('Are you sure you want to delete this service?')) {
  //     try {
  //       await adminAPI.deleteService(serviceId);
  //       loadData(); // Reload data
  //     } catch (error) {
  //       console.error('Error deleting service:', error);
  //       alert('Failed to delete service');
  //     }
  //   }
  // };

  const handleSaveService = async (serviceData: any) => {
    try {
      if (editingService) {
        await adminAPI.updateService(editingService.id, serviceData);
      } else {
        await adminAPI.createService(serviceData);
      }
      setShowServiceModal(false);
      setEditingService(null);
      loadData(); // Reload data
    } catch (error) {
      console.error('Error saving service:', error);
      alert('Failed to save service');
    }
  };

  // Toast notification functions
  const showToast = (message: string, type: 'success' | 'error' = 'success') => {
    setToastMessage(message);
    setToastType(type);
    setTimeout(() => setToastMessage(null), 5000);
  };

  const handleError = (message: string) => {
    showToast(message, 'error');
  };

  const handleSuccess = (message: string) => {
    showToast(message, 'success');
  };

  // Login logic removed - handled at route level

  const handleLogout = () => {
    // Clear any local storage and call parent logout
    localStorage.removeItem('currentUser');
    localStorage.removeItem('authToken');
    localStorage.removeItem('refreshToken');
    onLogout();
  };

  // Note: Appointment management functions moved to AppointmentManagement component

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return '#28a745';
      case 'pending': return '#ffc107';
      case 'completed': return '#6c757d';
      case 'cancelled': return '#dc3545';
      default: return '#6c757d';
    }
  };

  // Analytics calculations
  const totalRevenue = appointments
    .filter(apt => apt.status === 'completed')
    .reduce((sum, apt) => sum + ((apt as ExtendedAdminAppointment).totalPrice || 0), 0);

  // Note: pendingRevenue calculation removed as it's not used

  const todayAppointments = appointments.filter(apt => {
    const today = new Date().toDateString();
    return new Date(apt.date).toDateString() === today;
  });

  const upcomingAppointments = appointments.filter(apt => 
    new Date(apt.date) >= new Date() && apt.status !== 'cancelled'
  );

  // Authentication is now handled at route level

  if (!user || user.role !== 'admin') {
    return <div>Access denied. Admin privileges required.</div>;
  }

  return (
    <div className="dashboard-container admin" style={{ minHeight: '100vh', backgroundColor: '#f8fafc' }}>
      {/* Header */}
      <header className="dashboard-header" style={{
        backgroundColor: '#fff',
        borderBottom: '1px solid #e2e8f0',
        padding: '1rem',
        position: 'sticky',
        top: 0,
        zIndex: 1000
      }}>
        <div className="dashboard-header-content" style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          maxWidth: '1200px',
          margin: '0 auto',
          flexWrap: 'wrap',
          gap: '1rem'
        }}>
          <div className="dashboard-logo">
            <h1 style={{
              fontSize: 'clamp(1.2rem, 4vw, 1.8rem)',
              fontWeight: 'bold',
              color: '#1a202c',
              margin: 0
            }}>dammyspicybeauty - Admin</h1>
          </div>
          <div className="dashboard-user-menu" style={{
            display: 'flex',
            alignItems: 'center',
            gap: '1rem',
            flexWrap: 'wrap'
          }}>
            <span className="welcome-text" style={{
              fontSize: 'clamp(0.8rem, 2vw, 1rem)',
              color: '#4a5568'
            }}>Welcome, {user.firstName}!</span>
            <button className="logout-button" onClick={handleLogout} style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#e53e3e',
              color: 'white',
              border: 'none',
              borderRadius: '0.375rem',
              fontSize: '0.875rem',
              cursor: 'pointer'
            }}>
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="dashboard-main" style={{ padding: '1rem', maxWidth: '1200px', margin: '0 auto' }}>
        <div className="dashboard-content">
          {/* Navigation Tabs */}
          <div className="dashboard-tabs" style={{
            display: 'flex',
            gap: '0.5rem',
            marginBottom: '2rem',
            overflowX: 'auto',
            padding: '0.5rem 0',
            borderBottom: '1px solid #e2e8f0'
          }}>
            <button
              className={`tab-button ${activeTab === 'dashboard' ? 'active' : ''}`}
              onClick={() => setActiveTab('dashboard')}
              style={{
                padding: '0.75rem 1rem',
                border: 'none',
                borderRadius: '0.5rem',
                fontSize: 'clamp(0.7rem, 2vw, 0.875rem)',
                fontWeight: '500',
                cursor: 'pointer',
                whiteSpace: 'nowrap',
                backgroundColor: activeTab === 'dashboard' ? '#3b82f6' : '#f1f5f9',
                color: activeTab === 'dashboard' ? 'white' : '#64748b',
                transition: 'all 0.2s'
              }}
            >
              📊 DASHBOARD
            </button>
            <button
              className={`tab-button ${activeTab === 'appointments' ? 'active' : ''}`}
              onClick={() => setActiveTab('appointments')}
              style={{
                padding: '0.75rem 1rem',
                border: 'none',
                borderRadius: '0.5rem',
                fontSize: 'clamp(0.7rem, 2vw, 0.875rem)',
                fontWeight: '500',
                cursor: 'pointer',
                whiteSpace: 'nowrap',
                backgroundColor: activeTab === 'appointments' ? '#3b82f6' : '#f1f5f9',
                color: activeTab === 'appointments' ? 'white' : '#64748b',
                transition: 'all 0.2s'
              }}
            >
              📅 APPOINTMENTS
            </button>
            <button
              className={`tab-button ${activeTab === 'calendar' ? 'active' : ''}`}
              onClick={() => setActiveTab('calendar')}
              style={{
                padding: '0.75rem 1rem',
                border: 'none',
                borderRadius: '0.5rem',
                fontSize: 'clamp(0.7rem, 2vw, 0.875rem)',
                fontWeight: '500',
                cursor: 'pointer',
                whiteSpace: 'nowrap',
                backgroundColor: activeTab === 'calendar' ? '#3b82f6' : '#f1f5f9',
                color: activeTab === 'calendar' ? 'white' : '#64748b',
                transition: 'all 0.2s'
              }}
            >
              🗓️ CALENDAR
            </button>
            <button
              className={`tab-button ${activeTab === 'customers' ? 'active' : ''}`}
              onClick={() => setActiveTab('customers')}
              style={{
                padding: '0.75rem 1rem',
                border: 'none',
                borderRadius: '0.5rem',
                fontSize: 'clamp(0.7rem, 2vw, 0.875rem)',
                fontWeight: '500',
                cursor: 'pointer',
                whiteSpace: 'nowrap',
                backgroundColor: activeTab === 'customers' ? '#3b82f6' : '#f1f5f9',
                color: activeTab === 'customers' ? 'white' : '#64748b',
                transition: 'all 0.2s'
              }}
            >
              👥 CUSTOMERS
            </button>
            <button
              className={`tab-button ${activeTab === 'services' ? 'active' : ''}`}
              onClick={() => setActiveTab('services')}
              style={{
                padding: '0.75rem 1rem',
                border: 'none',
                borderRadius: '0.5rem',
                fontSize: 'clamp(0.7rem, 2vw, 0.875rem)',
                fontWeight: '500',
                cursor: 'pointer',
                whiteSpace: 'nowrap',
                backgroundColor: activeTab === 'services' ? '#3b82f6' : '#f1f5f9',
                color: activeTab === 'services' ? 'white' : '#64748b',
                transition: 'all 0.2s'
              }}
            >
              🛍️ SERVICES
            </button>
            <button
              className={`tab-button ${activeTab === 'reviews' ? 'active' : ''}`}
              onClick={() => setActiveTab('reviews')}
              style={{
                padding: '0.75rem 1rem',
                border: 'none',
                borderRadius: '0.5rem',
                fontSize: 'clamp(0.7rem, 2vw, 0.875rem)',
                fontWeight: '500',
                cursor: 'pointer',
                whiteSpace: 'nowrap',
                backgroundColor: activeTab === 'reviews' ? '#3b82f6' : '#f1f5f9',
                color: activeTab === 'reviews' ? 'white' : '#64748b',
                transition: 'all 0.2s'
              }}
            >
              ⭐ REVIEWS
            </button>

            <button
              className={`tab-button ${activeTab === 'branding' ? 'active' : ''}`}
              onClick={() => setActiveTab('branding')}
              style={{
                padding: '0.75rem 1rem',
                border: 'none',
                borderRadius: '0.5rem',
                fontSize: 'clamp(0.7rem, 2vw, 0.875rem)',
                fontWeight: '500',
                cursor: 'pointer',
                whiteSpace: 'nowrap',
                backgroundColor: activeTab === 'branding' ? '#3b82f6' : '#f1f5f9',
                color: activeTab === 'branding' ? 'white' : '#64748b',
                transition: 'all 0.2s'
              }}
            >
              🎨 BRANDING
            </button>

            <button
              className={`tab-button ${activeTab === 'calendar' ? 'active' : ''}`}
              onClick={() => setActiveTab('calendar')}
              style={{
                padding: '0.75rem 1rem',
                border: 'none',
                borderRadius: '0.5rem',
                fontSize: 'clamp(0.7rem, 2vw, 0.875rem)',
                fontWeight: '500',
                cursor: 'pointer',
                whiteSpace: 'nowrap',
                backgroundColor: activeTab === 'calendar' ? '#3b82f6' : '#f1f5f9',
                color: activeTab === 'calendar' ? 'white' : '#64748b',
                transition: 'all 0.2s'
              }}
            >
              📅 CALENDAR
            </button>

          </div>

          {/* Tab Content */}
          <div className="tab-content">
            {activeTab === 'dashboard' && (
              <div className="overview-section">
                {loading ? (
                  <LoadingSpinner message="Loading dashboard data..." />
                ) : (
                  <>
                    <div className="stats-grid admin-stats" style={{
                      display: 'grid',
                      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                      gap: '1rem',
                      marginBottom: '2rem'
                    }}>
                      <div className="stat-card" style={{
                        backgroundColor: 'white',
                        padding: '1.5rem',
                        borderRadius: '0.75rem',
                        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                        textAlign: 'center'
                      }}>
                        <h3 style={{
                          fontSize: 'clamp(1.5rem, 4vw, 2rem)',
                          fontWeight: 'bold',
                          color: '#1a202c',
                          margin: '0 0 0.5rem 0'
                        }}>{dashboardData?.overview?.totalAppointments || appointments.length}</h3>
                        <p style={{
                          fontSize: 'clamp(0.8rem, 2vw, 0.875rem)',
                          color: '#64748b',
                          margin: 0
                        }}>Total Appointments</p>
                      </div>
                      <div className="stat-card" style={{
                        backgroundColor: 'white',
                        padding: '1.5rem',
                        borderRadius: '0.75rem',
                        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                        textAlign: 'center'
                      }}>
                        <h3 style={{
                          fontSize: 'clamp(1.5rem, 4vw, 2rem)',
                          fontWeight: 'bold',
                          color: '#1a202c',
                          margin: '0 0 0.5rem 0'
                        }}>{dashboardData?.overview?.todayAppointments || todayAppointments.length}</h3>
                        <p style={{
                          fontSize: 'clamp(0.8rem, 2vw, 0.875rem)',
                          color: '#64748b',
                          margin: 0
                        }}>Today's Appointments</p>
                      </div>
                      <div className="stat-card" style={{
                        backgroundColor: 'white',
                        padding: '1.5rem',
                        borderRadius: '0.75rem',
                        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                        textAlign: 'center'
                      }}>
                        <h3 style={{
                          fontSize: 'clamp(1.5rem, 4vw, 2rem)',
                          fontWeight: 'bold',
                          color: '#1a202c',
                          margin: '0 0 0.5rem 0'
                        }}>{dashboardData?.overview?.totalUsers || users.filter(u => (u as ExtendedCustomer).role === 'user').length}</h3>
                        <p style={{
                          fontSize: 'clamp(0.8rem, 2vw, 0.875rem)',
                          color: '#64748b',
                          margin: 0
                        }}>Total Clients</p>
                      </div>
                      <div className="stat-card" style={{
                        backgroundColor: 'white',
                        padding: '1.5rem',
                        borderRadius: '0.75rem',
                        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                        textAlign: 'center'
                      }}>
                        <h3 style={{
                          fontSize: 'clamp(1.5rem, 4vw, 2rem)',
                          fontWeight: 'bold',
                          color: '#1a202c',
                          margin: '0 0 0.5rem 0'
                        }}>${(dashboardData?.overview?.totalRevenue || totalRevenue || 0).toFixed(2)}</h3>
                        <p style={{
                          fontSize: 'clamp(0.8rem, 2vw, 0.875rem)',
                          color: '#64748b',
                          margin: 0
                        }}>Total Revenue</p>
                      </div>
                    </div>

                    <div className="recent-activity">
                      <h3>Recent Appointments</h3>
                      <div className="appointments-list compact">
                        {(dashboardData?.recentAppointments || upcomingAppointments).slice(0, 5).map((appointment: any) => (
                          <div key={appointment.id || appointment._id} className="appointment-card compact">
                            <div className="appointment-header">
                              <h4>
                                {appointment.customerInfo?.name ||
                                 `${appointment.customerInfo?.firstName || ''} ${appointment.customerInfo?.lastName || ''}`.trim() ||
                                 appointment.user?.name || 'Guest'}
                              </h4>
                              <span
                                className="status-badge"
                                style={{ backgroundColor: getStatusColor(appointment.status) }}
                              >
                                {appointment.status.toUpperCase()}
                              </span>
                            </div>
                            <div className="appointment-details">
                              <p>{appointment.service?.name || appointment.serviceName}</p>
                              <p>{formatDate(appointment.date)} at {appointment.time}</p>
                              <p>${(appointment.service?.price || appointment.totalPrice || 0).toFixed(2)}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Appointment Status Overview */}
                    {dashboardData?.statistics?.appointments && (
                      <div className="status-overview">
                        <h3>Appointment Status Overview</h3>
                        <div className="status-grid">
                          {Object.entries(dashboardData.statistics.appointments).map(([status, count]: [string, any]) => (
                            <div key={status} className="status-stat">
                              <div className="status-count">{count}</div>
                              <div className="status-label">{status.charAt(0).toUpperCase() + status.slice(1)}</div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            )}

            {activeTab === 'appointments' && (
              <AppointmentManagement onAppointmentUpdate={loadData} />
            )}

            {activeTab === 'customers' && (
              <div className="users-section">
                <h3>Client Management</h3>
                <div className="users-list">
                  {users.map(customer => {
                    return (
                      <div key={customer.id} className="user-card">
                        <div className="user-header">
                          <h4>{customer.firstName} {customer.lastName}</h4>
                          <span className="user-stats">{customer.totalAppointments || 0} appointments</span>
                        </div>
                        <div className="user-details">
                          <p><strong>Email:</strong> {customer.email}</p>
                          <p><strong>Phone:</strong> {customer.phone}</p>
                          <p><strong>Member Since:</strong> {new Date(customer.createdAt).toLocaleDateString()}</p>
                          <p><strong>Total Spent:</strong> ${(customer.totalSpent || 0).toFixed(2)}</p>
                          <p><strong>Last Appointment:</strong> {customer.lastAppointment ? new Date(customer.lastAppointment).toLocaleDateString() : 'Never'}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {activeTab === 'services' && (
              <ServiceManagement
                onError={handleError}
                onSuccess={handleSuccess}
              />
            )}

            {activeTab === 'reviews' && (
              <ReviewManagement
                onError={handleError}
                onSuccess={handleSuccess}
              />
            )}

            {activeTab === 'calendar' && (
              <CalendarManagement />
            )}

            {activeTab === 'branding' && (
              <BrandingManagement
                onError={handleError}
                onSuccess={handleSuccess}
              />
            )}

          </div>
        </div>
      </main>

      {/* Service Modal */}
      <ServiceModal
        isOpen={showServiceModal}
        onClose={() => {
          setShowServiceModal(false);
          setEditingService(null);
        }}
        onSave={handleSaveService}
        service={editingService}
      />

      {/* Toast Notification */}
      {toastMessage && (
        <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
          toastType === 'success'
            ? 'bg-green-500 text-white'
            : 'bg-red-500 text-white'
        }`}>
          <div className="flex items-center justify-between">
            <span>{toastMessage}</span>
            <button
              onClick={() => setToastMessage(null)}
              className="ml-4 text-white hover:text-gray-200"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
